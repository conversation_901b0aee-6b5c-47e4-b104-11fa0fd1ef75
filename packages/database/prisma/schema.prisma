generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod"
  createInputTypes = "false"
  addSelectType    = "false"
  addIncludeType   = "false"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                           String                  @id @default(cuid())
  name                         String?
  email                        String                  @unique
  cpf                          String?
  avatarUrl                    String?
  bio                          String?
  role                         UserRole                @default(USER)
  onboardingComplete           Boolean                 @default(false)
  createdAt                    DateTime                @default(now())
  updatedAt                    DateTime                @updatedAt
  hashedPassword               String?
  emailVerified                Boolean                 @default(false)
  createdCoupons               Coupon[]                @relation("CreatedCoupons")
  subscriptions                Subscription[]
  Team                         Team[]
  TeamInvitation               TeamInvitation[]
  memberships                  TeamMembership[]
  oauthAccounts                UserOauthAccount[]
  oneTimePasswords             UserOneTimePassword[]
  sessions                     UserSession[]
  verificationTokens           UserVerificationToken[]
  createdAffiliateInvitations  AffiliateInvitation[]   @relation("CreatedAffiliateInvitations")
  affiliateProfile             AffiliateProfile?
  assets                       Asset[]                 @relation("CreatedAssets")
  certificates                 Certificate[]
  createdCoProducerInvitations CoProducerInvitation[]  @relation("CreatedCoProducerInvitations")
  coProducerRoles              CoProducer[]
  enrollments                  CourseEnrollment[]
  ebookPurchases               EbookPurchase[]
  ledgerEntries                LedgerEntry[]
  lessonProgress               LessonProgress[]
  mentoringSessions            MentoringSession[]
  affiliatedOrders             Order[]                 @relation("OrderAffiliate")
  orders                       Order[]                 @relation("OrderBuyer")
  createdProducts              Product[]               @relation("CreatedProducts")
  reviews                      Review[]
  createdInvitations           TeacherInvitation[]     @relation("CreatedTeacherInvitations")
  teacherProfile               TeacherProfile?
  withdraws                    Withdraw[]
  favoriteProducts             Product[]               @relation("FavoriteProducts")
  vitrines                     Vitrine[]               @relation("TeacherVitrines")

  @@map("users")
}

model UserSession {
  id             String   @id
  userId         String
  expiresAt      DateTime
  impersonatorId String?
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model UserOauthAccount {
  id             String @id @default(cuid())
  providerId     String
  providerUserId String
  userId         String
  user           User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([providerId, providerUserId])
}

model UserVerificationToken {
  id      String   @id @default(cuid())
  userId  String
  expires DateTime
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model UserOneTimePassword {
  id         String                  @id @default(cuid())
  userId     String
  code       String
  type       UserOneTimePasswordType
  identifier String
  expires    DateTime
  user       User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Team {
  id           String           @id @default(cuid())
  name         String
  avatarUrl    String?
  userId       String?
  subscription Subscription?
  User         User?            @relation(fields: [userId], references: [id])
  invitations  TeamInvitation[]
  memberships  TeamMembership[]
}

model TeamMembership {
  id        String         @id @default(cuid())
  teamId    String
  userId    String
  role      TeamMemberRole @default(MEMBER)
  isCreator Boolean        @default(false)
  team      Team           @relation(fields: [teamId], references: [id], onDelete: Cascade)
  user      User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
}

model TeamInvitation {
  id        String         @id @default(cuid())
  teamId    String
  email     String
  role      TeamMemberRole @default(MEMBER)
  createdAt DateTime       @default(now())
  expiresAt DateTime       @updatedAt
  userId    String?
  team      Team           @relation(fields: [teamId], references: [id], onDelete: Cascade)
  User      User?          @relation(fields: [userId], references: [id])

  @@unique([teamId, email])
}

model Subscription {
  id              String             @id
  teamId          String             @unique
  customerId      String
  status          SubscriptionStatus
  planId          String
  variantId       String
  nextPaymentDate DateTime?
  userId          String?
  team            Team               @relation(fields: [teamId], references: [id], onDelete: Cascade)
  User            User?              @relation(fields: [userId], references: [id])
}

model Offer {
  id                  String             @id @default(cuid())
  productId           String
  type                OfferType
  title               String
  description         String?
  price               Decimal            @db.Decimal(10, 2)
  targetProductId     String?
  active              Boolean            @default(true)
  order               Int                @default(0)
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  views               Int                @default(0)
  conversions         Int                @default(0)
  affiliateCommission Decimal?           @db.Decimal(5, 2)
  product             Product            @relation(fields: [productId], references: [id])
  targetProduct       Product?           @relation("TargetProduct", fields: [targetProductId], references: [id])
  interactions        OfferInteraction[]
  orderItems          OrderItem[]

  @@unique([productId, type, order])
  @@index([productId, type])
}

model Coupon {
  id         String    @id @default(cuid())
  code       String    @unique
  type       String
  value      Decimal   @db.Decimal(10, 2)
  maxUses    Int?
  usedCount  Int       @default(0)
  validFrom  DateTime?
  validUntil DateTime?
  active     Boolean   @default(true)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  creatorId  String
  isGlobal   Boolean   @default(true)
  productIds String[]
  creator    User      @relation("CreatedCoupons", fields: [creatorId], references: [id])
  Order      Order[]
}

model Category {
  id          String     @id @default(cuid())
  name        String     @unique
  slug        String     @unique
  description String?
  icon        String?
  color       String?
  isActive    Boolean    @default(true)
  sortOrder   Int        @default(0)
  parentId    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  parent      Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]

  @@map("categories")
}

model Product {
  id                    String                 @id @default(cuid())
  slug                  String                 @unique
  type                  ProductType            @default(COURSE)
  status                ProductStatus          @default(DRAFT)
  visibility            ProductVisibility      @default(PRIVATE)
  title                 String
  description           String?
  shortDescription      String?
  thumbnail             String?
  salesVideoUrl         String?
  creatorId             String
  price                 Decimal                @db.Decimal(10, 2)
  regularPrice          Decimal?               @db.Decimal(10, 2)
  enableInstallments    Boolean                @default(false)
  installmentsLimit     Int                    @default(1)
  acceptedPayments      String[]
  recurring             Boolean                @default(false)
  recurringInterval     String?
  trialDays             Int?
  checkoutType          CheckoutType           @default(DEFAULT)
  checkoutSettings      Json?
  customCheckoutUrl     String?
  successUrl            String?
  cancelUrl             String?
  termsUrl              String?
  enableAffiliate       Boolean                @default(false)
  affiliateCommission   Decimal?               @db.Decimal(5, 2)
  enableMarketplace     Boolean                @default(false)
  templateSettings      Json?
  welcomeEmail          Json?
  certificateTemplate   Json?
  bunnyCollectionId     String?
  integrations          Json?
  totalStudents         Int                    @default(0)
  rating                Decimal?               @db.Decimal(3, 2)
  totalReviews          Int                    @default(0)
  totalSales            Decimal                @default(0) @db.Decimal(10, 2)
  requiresApproval      Boolean                @default(false)
  metadata              Json?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  deletedAt             DateTime?
  checkoutBanner        String?
  categoryId            String?
  allowAffiliates       Boolean                @default(false)
  ebook                 Ebook?
  offers                Offer[]
  targetOffers          Offer[]                @relation("TargetProduct")
  abandonedCheckouts    AbandonedCheckout[]
  affiliateInvitations  AffiliateInvitation[]
  affiliateLinks        AffiliateLink[]
  assets                Asset[]                @relation("ProductAssets")
  coProducerInvitations CoProducerInvitation[]
  coProducers           CoProducer[]
  course                Course?
  ebookPurchases        EbookPurchase[]
  ebookSecurityConfig   EbookSecurityConfig?
  mentoring             Mentoring?
  orderItems            OrderItem[]
  orders                Order[]
  category              Category?              @relation(fields: [categoryId], references: [id])
  creator               User                   @relation("CreatedProducts", fields: [creatorId], references: [id])
  reviews               Review[]
  favorites             User[]                 @relation("FavoriteProducts")
  vitrineProducts       VitrineProduct[]

  @@index([status, type])
  @@index([creatorId])
  @@index([visibility])
  @@index([deletedAt])
  @@map("products")
}

model Ebook {
  id        String   @id @default(cuid())
  productId String   @unique
  fileUrl   String?
  pages     Int?
  format    String[]
  fileUrls  Json?
  settings  Json?
  chapters  Json?
  product   Product  @relation(fields: [productId], references: [id])
}

model Course {
  id                 String             @id @default(cuid())
  productId          String             @unique
  modules            Json
  certificateEnabled Boolean            @default(false)
  progressType       String             @default("SEQUENTIAL")
  completionCriteria Json?
  description        String?
  learningObjectives String[]
  title              String?
  totalDuration      Int                @default(0)
  certificates       Certificate[]
  enrollments        CourseEnrollment[]
  product            Product            @relation(fields: [productId], references: [id])

  @@map("courses")
}

model CourseEnrollment {
  id                  String                 @id @default(cuid())
  userId              String
  courseId            String
  lastAccessedAt      DateTime               @default(now())
  createdAt           DateTime               @default(now())
  updatedAt           DateTime               @updatedAt
  completedAt         DateTime?
  progress            Int                    @default(0)
  status              CourseEnrollmentStatus @default(ACTIVE)
  progress_percentage Int?                   @default(0)
  course              Course                 @relation(fields: [courseId], references: [id])
  user                User                   @relation(fields: [userId], references: [id])

  @@unique([userId, courseId], name: "userId_courseId")
  @@index([userId])
  @@index([courseId])
  @@map("course_enrollments")
}

model LessonProgress {
  id            String    @id @default(cuid())
  lessonId      String
  userId        String
  progress      Int       @default(0)
  viewedAt      DateTime?
  completedAt   DateTime?
  lastUpdatedAt DateTime  @default(now())
  user          User      @relation(fields: [userId], references: [id])

  @@unique([lessonId, userId], name: "lessonId_userId")
  @@index([userId])
  @@map("lesson_progress")
}

model Mentoring {
  id                 String              @id @default(cuid())
  productId          String              @unique
  duration           Int
  location           String              @default("ONLINE")
  maxStudents        Int                 @default(1)
  availability       Json
  timezone           String              @default("UTC")
  cancellationPolicy Json?
  rescheduleLimit    Int                 @default(2)
  advanceBooking     Int                 @default(24)
  autoGenerateLink   Boolean             @default(true)
  bufferTime         Int                 @default(15)
  instructions       String?
  meetingLink        String?
  meetingPlatform    String              @default("GOOGLE_MEET")
  description        String?
  product            Product             @relation(fields: [productId], references: [id])
  resources          MentoringResource[]
  sessions           MentoringSession[]

  @@map("mentoring")
}

model Asset {
  id          String      @id @default(cuid())
  fileName    String
  fileUrl     String
  filePath    String
  fileSize    BigInt
  mimeType    String
  fileType    AssetType
  context     String
  contextId   String?
  contextInfo Json?
  status      AssetStatus @default(ACTIVE)
  uploadedAt  DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  deletedAt   DateTime?
  creatorId   String
  productId   String?
  creator     User        @relation("CreatedAssets", fields: [creatorId], references: [id])
  product     Product?    @relation("ProductAssets", fields: [productId], references: [id])

  @@index([fileType])
  @@index([context])
  @@index([contextId])
  @@index([status])
  @@index([creatorId])
  @@index([deletedAt])
  @@index([productId])
  @@map("assets")
}

model MentoringSession {
  id          String        @id @default(cuid())
  mentoringId String
  studentId   String
  scheduledAt DateTime
  duration    Int
  status      SessionStatus @default(SCHEDULED)
  meetingLink String?
  notes       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  mentoring   Mentoring     @relation(fields: [mentoringId], references: [id])
  student     User          @relation(fields: [studentId], references: [id])

  @@index([mentoringId])
  @@index([studentId])
  @@index([scheduledAt])
  @@map("mentoring_sessions")
}

model MentoringResource {
  id          String    @id @default(cuid())
  mentoringId String
  title       String
  description String?
  type        String
  fileUrl     String?
  externalUrl String?
  metadata    Json?
  isPublic    Boolean   @default(true)
  order       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  mentoring   Mentoring @relation(fields: [mentoringId], references: [id])

  @@index([mentoringId])
  @@index([type])
  @@index([order])
  @@map("mentoring_resources")
}

model TeacherProfile {
  id          String              @id @default(cuid())
  userId      String              @unique
  specialties String[]
  approved    Boolean             @default(false)
  approvedAt  DateTime?
  bankAccount Json?
  invitations TeacherInvitation[]
  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("teacher_profiles")
}

model TeacherInvitation {
  id         String                  @id @default(cuid())
  email      String
  expiresAt  DateTime
  createdAt  DateTime                @default(now())
  createdBy  String
  token      String                  @unique
  status     TeacherInvitationStatus @default(PENDING)
  acceptedAt DateTime?
  teacherId  String?
  creator    User                    @relation("CreatedTeacherInvitations", fields: [createdBy], references: [id])
  teacher    TeacherProfile?         @relation(fields: [teacherId], references: [id])

  @@unique([email, token])
  @@map("teacher_invitations")
}

model AffiliateProfile {
  id             String          @id @default(cuid())
  userId         String          @unique
  commission     Decimal?        @db.Decimal(5, 2)
  totalSales     Decimal         @default(0) @db.Decimal(10, 2)
  bankAccount    Json?
  pixKey         String?
  affiliateLinks AffiliateLink[]
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("affiliate_profiles")
}

model AffiliateInvitation {
  id        String           @id @default(cuid())
  email     String
  code      String           @unique @db.VarChar(20)
  productId String
  createdBy String
  status    InvitationStatus @default(PENDING)
  expiresAt DateTime
  createdAt DateTime         @default(now())
  token     String?          @unique
  creator   User             @relation("CreatedAffiliateInvitations", fields: [createdBy], references: [id])
  product   Product          @relation(fields: [productId], references: [id])

  @@index([code])
  @@index([email])
  @@index([productId])
  @@index([token])
  @@map("affiliate_invitations")
}

model Order {
  id                String             @id @default(cuid())
  userId            String
  productId         String
  affiliateId       String?
  amount            Decimal            @db.Decimal(10, 2)
  status            OrderStatus        @default(PENDING)
  gatewayId         String?
  installments      Int                @default(1)
  paymentData       Json?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  couponId          String?
  orderType         OrderType          @default(MAIN)
  parentOrderId     String?
  ebookPurchases    EbookPurchase[]
  ledgerEntries     LedgerEntry[]
  offerInteractions OfferInteraction[]
  orderItems        OrderItem[]
  affiliate         User?              @relation("OrderAffiliate", fields: [affiliateId], references: [id])
  coupon            Coupon?            @relation(fields: [couponId], references: [id])
  parentOrder       Order?             @relation("OrderHierarchy", fields: [parentOrderId], references: [id])
  childOrders       Order[]            @relation("OrderHierarchy")
  product           Product            @relation(fields: [productId], references: [id])
  user              User               @relation("OrderBuyer", fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([parentOrderId])
  @@map("orders")
}

model CoProducer {
  id                String           @id @default(cuid())
  productId         String
  userId            String
  commissionPercent Decimal          @db.Decimal(5, 2)
  createdAt         DateTime         @default(now())
  role              String           @default("CO_PRODUCER")
  updatedAt         DateTime         @updatedAt
  status            CoProducerStatus @default(PENDING)
  product           Product          @relation(fields: [productId], references: [id])
  user              User             @relation(fields: [userId], references: [id])

  @@unique([productId, userId])
  @@map("co_producers")
}

model CoProducerInvitation {
  id         String                     @id @default(cuid())
  email      String
  code       String                     @unique @db.VarChar(20)
  token      String                     @unique
  productId  String
  createdBy  String
  status     CoProducerInvitationStatus @default(PENDING)
  commission Decimal                    @db.Decimal(5, 2)
  role       String                     @default("CO_PRODUCER")
  expiresAt  DateTime
  createdAt  DateTime                   @default(now())
  acceptedAt DateTime?
  creator    User                       @relation("CreatedCoProducerInvitations", fields: [createdBy], references: [id])
  product    Product                    @relation(fields: [productId], references: [id])

  @@index([code])
  @@index([email])
  @@index([productId])
  @@index([token])
  @@map("co_producer_invitations")
}

model AffiliateLink {
  id          String           @id @default(cuid())
  affiliateId String
  productId   String
  code        String           @unique
  clicks      Int              @default(0)
  conversions Int              @default(0)
  affiliate   AffiliateProfile @relation(fields: [affiliateId], references: [id])
  product     Product          @relation(fields: [productId], references: [id])

  @@map("affiliate_links")
}

model Review {
  id        String   @id @default(cuid())
  productId String
  userId    String
  rating    Int
  comment   String?
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([productId, userId])
  @@map("reviews")
}

model OfferInteraction {
  id        String               @id @default(cuid())
  orderId   String
  offerId   String
  action    OfferInteractionType
  metadata  Json?
  createdAt DateTime             @default(now())
  offer     Offer                @relation(fields: [offerId], references: [id], onDelete: Cascade)
  order     Order                @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([offerId])
  @@index([action])
  @@map("offer_interactions")
}

model OrderItem {
  id                  String   @id @default(cuid())
  orderId             String
  offerId             String?
  productId           String?
  quantity            Int      @default(1)
  unitPrice           Decimal  @db.Decimal(10, 2)
  totalPrice          Decimal  @db.Decimal(10, 2)
  affiliateCommission Decimal  @default(0) @db.Decimal(10, 2)
  offer               Offer?   @relation(fields: [offerId], references: [id])
  order               Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product             Product? @relation(fields: [productId], references: [id])

  @@index([orderId])
  @@index([offerId])
  @@index([productId])
  @@map("order_items")
}

model AbandonedCheckout {
  id                  String    @id @default(cuid())
  productId           String
  customerEmail       String
  customerName        String?
  formData            Json?
  abandonedAt         DateTime  @default(now())
  attempts            Int       @default(1)
  recoveryEmailSent   Boolean   @default(false)
  recoveryEmailSentAt DateTime?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  product             Product   @relation(fields: [productId], references: [id])

  @@index([customerEmail])
  @@index([productId])
  @@index([abandonedAt])
  @@map("abandoned_checkouts")
}

model Withdraw {
  id            String         @id @default(cuid())
  userId        String
  amount        Decimal        @db.Decimal(10, 2)
  status        WithdrawStatus @default(PENDING)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  ledgerEntries LedgerEntry[]
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("withdraws")
}

model Certificate {
  id                String            @id @default(cuid())
  userId            String
  courseId          String
  certificateNumber String            @unique
  studentName       String
  courseName        String
  courseDescription String?
  syllabus          String?
  completionDate    DateTime
  issueDate         DateTime          @default(now())
  validationCode    String            @unique
  instructorName    String
  instructorTitle   String?
  workload          Int
  status            CertificateStatus @default(PENDING)
  pdfUrl            String?
  metadata          Json?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  course            Course            @relation(fields: [courseId], references: [id], onDelete: Cascade)
  user              User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("certificates")
}

model EbookPurchase {
  id               String              @id @default(cuid())
  userId           String
  productId        String
  orderId          String?
  purchaseDate     DateTime            @default(now())
  downloadCount    Int                 @default(0)
  maxDownloads     Int                 @default(5)
  watermarkData    Json?
  securitySettings Json?
  validationCode   String              @unique
  status           EbookPurchaseStatus @default(ACTIVE)
  expiresAt        DateTime?
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  downloads        EbookDownload[]
  order            Order?              @relation(fields: [orderId], references: [id])
  product          Product             @relation(fields: [productId], references: [id], onDelete: Cascade)
  user             User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
  @@index([validationCode])
  @@map("ebook_purchases")
}

model EbookDownload {
  id               String        @id @default(cuid())
  ebookPurchaseId  String
  downloadedAt     DateTime      @default(now())
  ipAddress        String?
  userAgent        String?
  pdfUrl           String?
  watermarkApplied Boolean       @default(false)
  signatureApplied Boolean       @default(false)
  downloadHash     String?       @unique
  fileSize         BigInt?
  securityLevel    String        @default("STANDARD")
  metadata         Json?
  ebookPurchase    EbookPurchase @relation(fields: [ebookPurchaseId], references: [id], onDelete: Cascade)

  @@index([ebookPurchaseId])
  @@index([downloadedAt])
  @@map("ebook_downloads")
}

model EbookSecurityConfig {
  id                 String   @id @default(cuid())
  productId          String   @unique
  watermarkEnabled   Boolean  @default(true)
  printAllowed       Boolean  @default(false)
  copyAllowed        Boolean  @default(false)
  maxDownloads       Int      @default(5)
  watermarkPosition  String   @default("DIAGONAL")
  watermarkOpacity   Float    @default(0.3)
  watermarkText      String?
  enableSignature    Boolean  @default(true)
  signatureType      String   @default("DIGITAL")
  expirationDays     Int?
  allowOfflineAccess Boolean  @default(true)
  trackingEnabled    Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  product            Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@map("ebook_security_configs")
}

model LedgerEntry {
  id          String          @id @default(cuid())
  userId      String
  orderId     String?
  withdrawId  String?
  type        LedgerEntryType
  direction   LedgerDirection
  amount      Decimal         @db.Decimal(10, 2)
  description String?
  createdAt   DateTime        @default(now())
  availableAt DateTime        @default(now())
  order       Order?          @relation(fields: [orderId], references: [id])
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  withdraw    Withdraw?       @relation(fields: [withdrawId], references: [id])

  @@index([userId, availableAt])
  @@index([orderId])
  @@index([withdrawId])
  @@map("ledger_entries")
}

enum SessionStatus {
  SCHEDULED
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum TeacherInvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

enum CoProducerStatus {
  PENDING
  ACTIVE
  INACTIVE
  REJECTED
}

enum CoProducerInvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
  REJECTED
}

enum TeamMemberRole {
  MEMBER
  OWNER
}

enum UserOneTimePasswordType {
  SIGNUP
  LOGIN
  PASSWORD_RESET
  TEACHER_SIGNUP
}

enum SubscriptionStatus {
  TRIALING
  ACTIVE
  PAUSED
  CANCELED
  PAST_DUE
  UNPAID
  INCOMPLETE
  EXPIRED
  PENDING
}

enum UserRole {
  USER
  TEACHER
  AFFILIATE
  ADMIN
}

enum ProductType {
  COURSE
  MENTORING
  EBOOK
}

enum ProductStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum ProductVisibility {
  PUBLIC
  PRIVATE
  UNLISTED
}

enum CheckoutType {
  DEFAULT
  CUSTOM
  EXTERNAL
}

enum ContentType {
  VIDEO
  TEXT
  FILE
  QUIZ
  ASSIGNMENT
}

enum OfferType {
  UPSELL
  DOWNSELL
  ORDER_BUMP
}

enum OrderStatus {
  PENDING
  PROCESSING
  PAID
  FAILED
  REFUNDED
  CANCELLED
}

enum OrderType {
  MAIN
  ORDER_BUMP
  UPSELL
  DOWNSELL
}

enum OfferInteractionType {
  VIEWED
  ACCEPTED
  REJECTED
  ABANDONED
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
}

enum WithdrawStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum LedgerDirection {
  CREDIT
  DEBIT
}

enum LedgerEntryType {
  SALE_GROSS
  PLATFORM_FEE
  GATEWAY_FEE
  AFFILIATE_COMMISSION_CREDIT
  AFFILIATE_COMMISSION_DEBIT
  COPRODUCER_COMMISSION_CREDIT
  COPRODUCER_COMMISSION_DEBIT
  REFUND
  WITHDRAWAL
  WITHDRAWAL_FEE
  ADJUSTMENT_CREDIT
  ADJUSTMENT_DEBIT
}

enum AssetType {
  EBOOK
  VIDEO
  IMAGE
  DOCUMENT
  AUDIO
  ARCHIVE
  OTHER
}

enum AssetStatus {
  ACTIVE
  DELETED
  PROCESSING
  ERROR
}

enum CertificateStatus {
  PENDING
  GENERATED
  DELIVERED
  ERROR
}

enum EbookPurchaseStatus {
  ACTIVE
  EXPIRED
  REVOKED
  SUSPENDED
}

enum CourseEnrollmentStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  COMPLETED
  CANCELLED
}

// Modelos para Sistema de Vitrines
model Vitrine {
  id          String   @id @default(cuid())
  name        String
  description String?
  banner      String?
  theme       Json? // Cores, layout, configurações visuais
  isActive    Boolean  @default(true)
  teacherId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  teacher  User             @relation("TeacherVitrines", fields: [teacherId], references: [id], onDelete: Cascade)
  sections VitrineSection[]

  @@map("vitrines")
}

model VitrineSection {
  id        String   @id @default(cuid())
  title     String
  order     Int
  vitrineId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  vitrine  Vitrine          @relation(fields: [vitrineId], references: [id], onDelete: Cascade)
  products VitrineProduct[]

  @@map("vitrine_sections")
}

model VitrineProduct {
  id        String   @id @default(cuid())
  sectionId String
  productId String
  order     Int
  createdAt DateTime @default(now())

  section VitrineSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  product Product        @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([sectionId, productId])
  @@map("vitrine_products")
}
