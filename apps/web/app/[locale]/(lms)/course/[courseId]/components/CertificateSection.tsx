'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Award, Download, ExternalLink, Clock, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { apiClient } from '@shared/lib/api-client';
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';

interface CertificateSectionProps {
  courseId: string;
  isCompleted: boolean;
  completionPercentage: number;
}

interface Certificate {
  id: string;
  certificateCode: string;
  validationCode: string;
  status: 'ACTIVE' | 'REVOKED';
  issuedAt: Date;
  pdfUrl?: string;
}

export default function CertificateSection({ 
  courseId, 
  isCompleted, 
  completionPercentage 
}: CertificateSectionProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const { data: certificates, refetch } = useQuery({
    queryKey: ['course-certificates', courseId],
    queryFn: () => apiClient.certificates.list.query({ courseId }),
    enabled: isCompleted
  });

  const generateMutation = useMutation({
    mutationFn: () => apiClient.certificates.generate.mutate({ courseId }),
    onSuccess: () => {
      toast.success('Certificado gerado com sucesso!');
      refetch();
      setIsGenerating(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao gerar certificado');
      setIsGenerating(false);
    }
  });

  const downloadMutation = useMutation({
    mutationFn: (certificateId: string) => 
      apiClient.certificates.download.mutate({ certificateId }),
    onSuccess: (data) => {
      window.open(data.downloadUrl, '_blank');
      toast.success('Download iniciado!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao baixar certificado');
    }
  });

  const handleGenerate = () => {
    setIsGenerating(true);
    generateMutation.mutate();
  };

  const handleDownload = (certificateId: string) => {
    downloadMutation.mutate(certificateId);
  };

  const handleValidate = (validationCode: string) => {
    const url = `/validate-certificate?code=${validationCode}`;
    window.open(url, '_blank');
  };

  if (!isCompleted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Certificado de Conclusão
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">
              Complete o curso para obter seu certificado
            </h3>
            <p className="text-gray-600 mb-4">
              Progresso atual: {completionPercentage.toFixed(1)}%
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const activeCertificate = certificates?.find(cert => cert.status === 'ACTIVE');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Certificado de Conclusão
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!activeCertificate ? (
          <div className="text-center py-6">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">
              Parabéns! Você concluiu o curso
            </h3>
            <p className="text-gray-600 mb-6">
              Gere seu certificado de conclusão agora mesmo.
            </p>
            <Button 
              onClick={handleGenerate}
              disabled={isGenerating}
              size="lg"
              className="gap-2"
            >
              <Award className="h-4 w-4" />
              {isGenerating ? 'Gerando...' : 'Gerar Certificado'}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    Certificado Válido
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  Emitido em {format(new Date(activeCertificate.issuedAt), 'dd/MM/yyyy', { locale: ptBR })}
                </p>
                <p className="text-xs text-gray-500 font-mono">
                  Código: {activeCertificate.certificateCode}
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button 
                onClick={() => handleDownload(activeCertificate.id)}
                disabled={downloadMutation.isPending}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                {downloadMutation.isPending ? 'Baixando...' : 'Baixar PDF'}
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => handleValidate(activeCertificate.validationCode)}
                className="gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                Validar
              </Button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-1">
                Como validar seu certificado?
              </h4>
              <p className="text-blue-800 text-sm">
                Use o código de validação {activeCertificate.validationCode} em nossa página de validação pública.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}