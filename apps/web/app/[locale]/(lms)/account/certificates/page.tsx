'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@shared/lib/api-client';
import { CertificatesShowcase } from '@/components/certificates/certificates-showcase';
import { useUser } from '@saas/auth/hooks/use-user';

interface Certificate {
  id: string;
  certificateCode: string;
  validationCode: string;
  status: 'ACTIVE' | 'REVOKED';
  issuedAt: Date;
  pdfUrl?: string;
  course: {
    id: string;
    title: string;
    product: {
      name: string;
      imageUrl?: string;
    };
  };
}

export default function CertificatesPage() {
  const { user } = useUser();

  const { data: certificates, isLoading } = useQuery({
    queryKey: ['certificates'],
    queryFn: () => apiClient.certificates.list.query()
  });

  // Transform API data to match the new component interface
  const transformedCertificates = certificates?.map(cert => ({
    id: cert.id,
    courseTitle: cert.course.product.name,
    courseType: 'CURSO' as const, // Default to CURSO for now
    issueDate: new Date(cert.issuedAt).toLocaleDateString('pt-BR'),
    downloadUrl: cert.pdfUrl || '#',
    image: '/images/certificate-placeholder.jpg', // Default certificate image
    courseImage: cert.course.product.imageUrl || '/images/course-placeholder.jpg',
    instructor: 'Instrutor do Curso', // This would come from the API
    duration: '8 horas', // This would come from the API
    grade: undefined, // This would come from the API if available
    isVerified: cert.status === 'ACTIVE',
    shareUrl: `/certificates/${cert.id}/share`
  })) || [];

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <CertificatesShowcase
      certificates={transformedCertificates}
      userName={user?.name || 'Aluno'}
    />
  );
}
