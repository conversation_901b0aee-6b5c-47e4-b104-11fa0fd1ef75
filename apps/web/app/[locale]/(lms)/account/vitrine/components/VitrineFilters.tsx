"use client";

import { useState } from "react";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { SearchIcon, FilterIcon } from "lucide-react";

export function VitrineFilters() {
  const [search, setSearch] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");

  const filters = [
    { id: "all", label: "Todas" },
    { id: "marketing", label: "Marketing" },
    { id: "tech", label: "Tecnologia" },
    { id: "business", label: "Negócios" },
    { id: "design", label: "Design" }
  ];

  return (
    <div className="mb-8 space-y-4">
      {/* Search */}
      <div className="relative max-w-md">
        <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Buscar vitrines..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-10 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
        />
      </div>

      {/* Filters */}
      <div className="flex items-center gap-2 flex-wrap">
        <FilterIcon className="w-4 h-4 text-gray-400" />
        {filters.map((filter) => (
          <Button
            key={filter.id}
            variant={activeFilter === filter.id ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveFilter(filter.id)}
            className={
              activeFilter === filter.id
                ? "bg-purple-600 hover:bg-purple-700"
                : "border-gray-600 text-gray-300 hover:bg-gray-800"
            }
          >
            {filter.label}
          </Button>
        ))}
      </div>
    </div>
  );
}