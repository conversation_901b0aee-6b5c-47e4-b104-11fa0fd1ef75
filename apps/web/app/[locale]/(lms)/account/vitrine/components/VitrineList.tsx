"use client";

import { useState, useEffect } from "react";
import { VitrineCard } from "./VitrineCard";
import { MOCK_VITRINES } from "../data/mock-vitrines";

export function VitrineList() {
  const [vitrines, setVitrines] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simular carregamento
    setTimeout(() => {
      setVitrines(MOCK_VITRINES);
      setLoading(false);
    }, 800);
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-gray-800 rounded-xl h-80 animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {vitrines.map((vitrine) => (
        <VitrineCard key={vitrine.id} vitrine={vitrine} />
      ))}
    </div>
  );
}