"use client";

import { useState, useRef } from "react";
import { ChevronLeftIcon, ChevronRightIcon, PlayIcon, StarIcon, ClockIcon, UsersIcon, BookOpenIcon } from "lucide-react";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";

interface Product {
  id: string;
  title: string;
  thumbnail: string;
  price: number;
  type: string;
  duration?: string;
  pages?: number;
  students?: number;
  downloads?: number;
  sessions?: number;
  rating: number;
}

interface ProductCarouselProps {
  title: string;
  products: Product[];
}

export function ProductCarousel({ title, products }: ProductCarouselProps) {
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = 320; // largura do card + gap
      scrollRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'COURSE':
        return <PlayIcon className="w-4 h-4" />;
      case 'EBOOK':
        return <BookOpenIcon className="w-4 h-4" />;
      case 'MENTORING':
        return <UsersIcon className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'COURSE':
        return 'Curso';
      case 'EBOOK':
        return 'E-book';
      case 'MENTORING':
        return 'Mentoria';
      default:
        return type;
    }
  };

  return (
    <div className="mb-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => scroll('left')}
            className="text-white hover:bg-white/10 rounded-full w-10 h-10 p-0"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => scroll('right')}
            className="text-white hover:bg-white/10 rounded-full w-10 h-10 p-0"
          >
            <ChevronRightIcon className="w-5 h-5" />
          </Button>
        </div>
      </div>

      <div 
        ref={scrollRef}
        className="flex gap-4 overflow-x-auto scrollbar-hide scroll-smooth"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {products.map((product) => (
          <div
            key={product.id}
            className="flex-shrink-0 w-80 group cursor-pointer"
            onMouseEnter={() => setHoveredProduct(product.id)}
            onMouseLeave={() => setHoveredProduct(null)}
          >
            <div className="relative bg-gray-900 rounded-lg overflow-hidden transition-all duration-300 group-hover:scale-105 group-hover:z-10">
              {/* Thumbnail */}
              <div className="relative h-44 overflow-hidden">
                <img
                  src={product.thumbnail}
                  alt={product.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors" />
                
                {/* Type Badge */}
                <Badge className="absolute top-3 left-3 bg-black/60 text-white border-0">
                  {getTypeIcon(product.type)}
                  <span className="ml-1">{getTypeLabel(product.type)}</span>
                </Badge>

                {/* Play Button */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                    <PlayIcon className="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-white mb-2 line-clamp-2 group-hover:text-purple-400 transition-colors">
                  {product.title}
                </h3>

                {/* Stats */}
                <div className="flex items-center gap-4 mb-3 text-sm text-gray-400">
                  {product.duration && (
                    <div className="flex items-center gap-1">
                      <ClockIcon className="w-4 h-4" />
                      <span>{product.duration}</span>
                    </div>
                  )}
                  {product.pages && (
                    <div className="flex items-center gap-1">
                      <BookOpenIcon className="w-4 h-4" />
                      <span>{product.pages} páginas</span>
                    </div>
                  )}
                  {product.students && (
                    <div className="flex items-center gap-1">
                      <UsersIcon className="w-4 h-4" />
                      <span>{product.students}</span>
                    </div>
                  )}
                  {product.downloads && (
                    <div className="flex items-center gap-1">
                      <UsersIcon className="w-4 h-4" />
                      <span>{product.downloads}</span>
                    </div>
                  )}
                </div>

                {/* Rating & Price */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <StarIcon className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-white">{product.rating}</span>
                  </div>
                  <span className="text-lg font-bold text-green-400">
                    {formatPrice(product.price)}
                  </span>
                </div>

                {/* Hover Actions */}
                {hoveredProduct === product.id && (
                  <div className="mt-4 flex gap-2">
                    <Button size="sm" className="flex-1 bg-red-600 hover:bg-red-700">
                      Ver Detalhes
                    </Button>
                    <Button size="sm" variant="outline" className="border-white text-white hover:bg-white hover:text-black">
                      Comprar
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}