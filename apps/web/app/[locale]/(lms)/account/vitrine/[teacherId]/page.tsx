"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { ProductCarousel } from "../components/ProductCarousel";
import { Button } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { PlayIcon, StarIcon, UsersIcon, BookOpenIcon, ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { MOCK_VITRINES } from "../data/mock-vitrines";

export default function VitrineTeacherPage() {
  const params = useParams();
  const teacherId = params.teacherId as string;
  const [vitrine, setVitrine] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simular carregamento
    setTimeout(() => {
      const foundVitrine = MOCK_VITRINES.find(v => v.teacher.id === teacherId);
      setVitrine(foundVitrine);
      setLoading(false);
    }, 500);
  }, [teacherId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Carregando vitrine...</div>
      </div>
    );
  }

  if (!vitrine) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-4">Vitrine não encontrada</h1>
          <Link href="/account/vitrine">
            <Button>Voltar às Vitrines</Button>
          </Link>
        </div>
      </div>
    );
  }

  // Produto em destaque (primeiro da primeira seção)
  const featuredProduct = vitrine.sections[0]?.products[0];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Section com produto em destaque */}
      <div className="relative h-[80vh] overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0">
          <img
            src={featuredProduct?.thumbnail || vitrine.banner}
            alt={vitrine.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black via-black/70 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
        </div>

        {/* Content */}
        <div className="relative container mx-auto px-4 h-full flex items-center">
          <div className="max-w-2xl">
            {/* Back Button */}
            <Link href="/account/vitrine" className="inline-flex items-center gap-2 text-gray-300 hover:text-white mb-6 transition-colors">
              <ArrowLeftIcon className="w-4 h-4" />
              Voltar às Vitrines
            </Link>

            {/* Teacher Info */}
            <div className="flex items-center gap-4 mb-6">
              <Avatar className="w-16 h-16 border-2 border-white">
                <AvatarImage src={vitrine.teacher.image} />
                <AvatarFallback>{vitrine.teacher.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-semibold">{vitrine.teacher.name}</h2>
                <p className="text-gray-300">{vitrine.teacher.bio}</p>
              </div>
            </div>

            {/* Vitrine Title */}
            <h1 className="text-5xl font-bold mb-4 leading-tight">
              {vitrine.name}
            </h1>

            <p className="text-xl text-gray-300 mb-8">
              {vitrine.description}
            </p>

            {/* Featured Product Info */}
            {featuredProduct && (
              <div className="mb-8">
                <Badge className="mb-4 bg-red-600 text-white">
                  ⭐ Produto em Destaque
                </Badge>
                <h3 className="text-2xl font-bold mb-2">{featuredProduct.title}</h3>
                <div className="flex items-center gap-6 text-sm text-gray-300 mb-6">
                  {featuredProduct.duration && (
                    <span>⏱️ {featuredProduct.duration}</span>
                  )}
                  {featuredProduct.students && (
                    <span>👥 {featuredProduct.students} alunos</span>
                  )}
                  <span>⭐ {featuredProduct.rating}</span>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button size="lg" className="bg-red-600 hover:bg-red-700">
                <PlayIcon className="w-5 h-5 mr-2" />
                Ver Produto
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-black">
                Mais Informações
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Product Sections */}
      <div className="container mx-auto px-4 py-12">
        {vitrine.sections.map((section: any) => (
          <ProductCarousel
            key={section.id}
            title={section.title}
            products={section.products}
          />
        ))}

        {/* Teacher Bio Section */}
        <div className="mt-16 bg-gray-900 rounded-xl p-8">
          <div className="flex items-start gap-6">
            <Avatar className="w-20 h-20">
              <AvatarImage src={vitrine.teacher.image} />
              <AvatarFallback>{vitrine.teacher.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="text-2xl font-bold mb-4">Sobre {vitrine.teacher.name}</h3>
              <p className="text-gray-300 leading-relaxed">
                {vitrine.teacher.bio}
              </p>
              
              {/* Stats */}
              <div className="flex gap-8 mt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">
                    {vitrine.sections.reduce((acc: number, section: any) => acc + section.products.length, 0)}
                  </div>
                  <div className="text-sm text-gray-400">Produtos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {vitrine.sections
                      .flatMap((s: any) => s.products)
                      .reduce((acc: number, p: any) => acc + (p.students || p.downloads || 0), 0)
                      .toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-400">Alunos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">4.8</div>
                  <div className="text-sm text-gray-400">Avaliação</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}