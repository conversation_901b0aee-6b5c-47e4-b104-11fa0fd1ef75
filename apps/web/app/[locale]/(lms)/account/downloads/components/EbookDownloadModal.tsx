'use client';

import { useState } from 'react';
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@ui/components/dialog';
import { Button } from '@ui/components/button';
import {
	Download,
	FileText,
	AlertTriangle,
	CheckCircle2,
	Clock,
} from 'lucide-react';
import { apiClient } from '@shared/lib/api-client';
import { toast } from 'sonner';

interface EbookDownloadModalProps {
	product: {
		id: string;
		title: string;
		ebook?: {
			id: string;
			fileUrl?: string | null;
			fileUrls?: any;
			format?: string[];
		} | null;
	};
	isOpen: boolean;
	onClose: () => void;
	downloadCount: number;
	maxDownloads: number;
}

export function EbookDownloadModal({
	product,
	isOpen,
	onClose,
	downloadCount,
	maxDownloads
}: EbookDownloadModalProps) {
	const [isDownloading, setIsDownloading] = useState(false);

	// Função utilitária para iniciar download (mesma lógica do certificado)
	const triggerDownload = async (downloadUrl: string, fileName: string) => {
		console.log('🔍 triggerDownload chamada:', { downloadUrl: downloadUrl.substring(0, 50) + '...', fileName });

		try {
			if (downloadUrl.startsWith('data:')) {
				console.log('🔍 Processando data URL');

				// Converter data URL para blob
				const response = await fetch(downloadUrl);
				const blob = await response.blob();
				const blobUrl = URL.createObjectURL(blob);

				console.log('🔍 Blob criado, iniciando download');

				// Criar link temporário
				const link = document.createElement('a');
				link.href = blobUrl;
				link.download = fileName || 'ebook.pdf';
				link.style.display = 'none';

				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				// Limpar o blob URL após um tempo
				setTimeout(() => {
					URL.revokeObjectURL(blobUrl);
					console.log('🔍 Blob URL limpo');
				}, 1000);

				console.log('🔍 Download iniciado via data URL');

			} else if (downloadUrl.startsWith('blob:')) {
				console.log('🔍 Processando blob URL');

				// Criar link temporário
				const link = document.createElement('a');
				link.href = downloadUrl;
				link.download = fileName || 'ebook.pdf';
				link.style.display = 'none';

				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				console.log('🔍 Download iniciado via blob URL');

			} else {
				console.log('🔍 Abrindo URL normal em nova aba');
				// Para URLs normais, abrir em nova aba
				window.open(downloadUrl, '_blank');
			}
		} catch (error) {
			console.error('🔍 Erro em triggerDownload:', error);
			// Fallback final: tentar abrir a URL diretamente
			window.open(downloadUrl, '_blank');
		}
	};

	// Download mutation
	const downloadEbook = apiClient.ebooks.download.useMutation({
		onSuccess: async (result) => {
			console.log('🔍 downloadEbook onSuccess:', result);

			if (result.downloadUrl) {
				// Gerar nome do arquivo baseado no produto
				const fileName = `ebook-${product.title}-${Date.now()}.pdf`;
				console.log('🔍 Chamando triggerDownload com:', {
					downloadUrl: result.downloadUrl.substring(0, 50) + '...',
					fileName
				});
				await triggerDownload(result.downloadUrl, fileName);
				toast.success('Download iniciado! Seu ebook personalizado está sendo preparado.');
				onClose();
			} else {
				console.log('🔍 Sem URL de download');
				toast.error('URL de download não disponível');
			}
			setIsDownloading(false);
		},
		onError: (error) => {
			toast.error('Erro ao baixar o ebook');
			console.error('Download error:', error);
			setIsDownloading(false);
		}
	});

	const remainingDownloads = maxDownloads - downloadCount;

	const handleDownload = async () => {
		if (remainingDownloads <= 0) {
			toast.error('Você atingiu o limite de downloads para este e-book');
			return;
		}

		setIsDownloading(true);
		try {
			await downloadEbook.mutateAsync({
				productId: product.id,
				format: 'PDF',
				includeWatermark: true,
				includeSignature: true,
				securityLevel: 'STANDARD'
			});
		} catch (error) {
			console.error('Download error:', error);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Download className="h-5 w-5 text-green-600" />
						Baixar E-book
					</DialogTitle>
				</DialogHeader>

				<div className="space-y-6">
					{/* Product info */}
					<div className="bg-gray-50 rounded-lg p-4">
						<h3 className="font-medium text-gray-900 mb-2">{product.title}</h3>
						<div className="flex items-center gap-2 text-sm text-gray-600">
							<FileText className="h-4 w-4" />
							<span>E-book Digital</span>
						</div>
					</div>

					{/* Download limit warning */}
					{remainingDownloads <= 2 && (
						<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
							<div className="flex items-start gap-2">
								<AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-yellow-800">Atenção: Limite de Downloads</h4>
									<p className="text-sm text-yellow-700 mt-1">
										Você tem apenas {remainingDownloads} download{remainingDownloads !== 1 ? 's' : ''} restante{remainingDownloads !== 1 ? 's' : ''} para este e-book.
									</p>
								</div>
							</div>
						</div>
					)}

					{/* Simple info */}
					<div className="bg-green-50 rounded-lg p-4">
						<div className="flex items-start gap-2">
							<CheckCircle2 className="h-4 w-4 text-green-600 mt-0.5" />
							<div className="text-sm text-green-800">
								<p className="font-medium mb-1">Seu e-book personalizado</p>
								<p className="text-xs">
									O arquivo será personalizado com suas informações e pronto para download.
								</p>
							</div>
						</div>
					</div>

					{/* Download count */}
					<div className="flex items-center justify-between text-sm text-gray-600">
						<span>Downloads utilizados:</span>
						<span className="font-medium">{downloadCount}/{maxDownloads}</span>
					</div>

					{/* Action buttons */}
					<div className="flex gap-3 pt-4">
						<Button
							onClick={onClose}
							variant="outline"
							className="flex-1"
							disabled={isDownloading}
						>
							Cancelar
						</Button>
						<Button
							onClick={handleDownload}
							className="flex-1 bg-green-600 hover:bg-green-700"
							disabled={isDownloading || remainingDownloads <= 0}
						>
							{isDownloading ? (
								<>
									<Clock className="h-4 w-4 mr-2 animate-spin" />
									Preparando...
								</>
							) : (
								<>
									<Download className="h-4 w-4 mr-2" />
									Baixar E-book
								</>
							)}
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
