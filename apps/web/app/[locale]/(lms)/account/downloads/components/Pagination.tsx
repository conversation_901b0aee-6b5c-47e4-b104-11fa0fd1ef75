'use client';

import { Button } from '@ui/components/button';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

interface PaginationProps {
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	itemsPerPage: number;
	totalItems: number;
}

export function Pagination({
	currentPage,
	totalPages,
	onPageChange,
	itemsPerPage,
	totalItems
}: PaginationProps) {
	const getVisiblePages = () => {
		const delta = 2;
		const range = [];
		const rangeWithDots = [];

		for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
			range.push(i);
		}

		if (currentPage - delta > 2) {
			rangeWithDots.push(1, '...');
		} else {
			rangeWithDots.push(1);
		}

		rangeWithDots.push(...range);

		if (currentPage + delta < totalPages - 1) {
			rangeWithDots.push('...', totalPages);
		} else if (totalPages > 1) {
			rangeWithDots.push(totalPages);
		}

		return rangeWithDots;
	};

	const startItem = (currentPage - 1) * itemsPerPage + 1;
	const endItem = Math.min(currentPage * itemsPerPage, totalItems);

	if (totalPages <= 1) return null;

	return (
		<div className="flex flex-col sm:flex-row items-center justify-between gap-4 py-4">
			{/* Items info */}
			<div className="text-sm text-gray-600">
				Mostrando {startItem} a {endItem} de {totalItems} e-books
			</div>

			{/* Pagination controls */}
			<div className="flex items-center gap-1">
				{/* Previous button */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(currentPage - 1)}
					disabled={currentPage === 1}
					className="h-8 w-8 p-0"
				>
					<ChevronLeft className="h-4 w-4" />
				</Button>

				{/* Page numbers */}
				<div className="flex items-center gap-1">
					{getVisiblePages().map((page, index) => (
						<>
							{page === '...' ? (
								<div key={`dots-${index}`} className="px-2 py-1">
									<MoreHorizontal className="h-4 w-4 text-gray-400" />
								</div>
							) : (
								<Button
									key={page}
									variant={currentPage === page ? "default" : "outline"}
									size="sm"
									onClick={() => onPageChange(page as number)}
									className="h-8 w-8 p-0"
								>
									{page}
								</Button>
							)}
						</>
					))}
				</div>

				{/* Next button */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(currentPage + 1)}
					disabled={currentPage === totalPages}
					className="h-8 w-8 p-0"
				>
					<ChevronRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
}
