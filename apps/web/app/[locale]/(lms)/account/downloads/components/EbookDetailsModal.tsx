'use client';

import { useState } from 'react';
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@ui/components/dialog';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import {
	Download,
	FileText,
	Calendar,
	Shield,
	Clock,
	BookOpen,
	Eye,
	Info,
	CheckCircle2,
} from 'lucide-react';

interface EbookDetailsModalProps {
	product: {
		id: string;
		title: string;
		description?: string | null;
		thumbnail?: string | null;
		ebook?: {
			id: string;
			fileUrl?: string | null;
			fileUrls?: any;
			format?: string[];
		} | null;
	};
	isOpen: boolean;
	onClose: () => void;
	onDownload: () => void;
}

export function EbookDetailsModal({ product, isOpen, onClose, onDownload }: EbookDetailsModalProps) {
	const getFileFormats = () => {
		if (product.ebook?.fileUrls) {
			return Object.keys(product.ebook.fileUrls);
		}
		if (product.ebook?.fileUrl) {
			return ['PDF'];
		}
		return [];
	};

	const formats = getFileFormats();

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<BookOpen className="h-5 w-5 text-green-600" />
						Detalhes do E-book
					</DialogTitle>
				</DialogHeader>

				<div className="space-y-6">
					{/* Header with thumbnail and title */}
					<div className="flex gap-4">
						{product.thumbnail ? (
							<div
								className="w-32 h-40 bg-cover bg-center rounded-lg flex-shrink-0"
								style={{ backgroundImage: `url(${product.thumbnail})` }}
							/>
						) : (
							<div className="w-32 h-40 bg-gradient-to-br from-green-100 to-green-50 rounded-lg flex items-center justify-center flex-shrink-0">
								<BookOpen className="h-12 w-12 text-green-600" />
							</div>
						)}

						<div className="flex-1 space-y-3">
							<div>
								<h2 className="text-xl font-semibold text-gray-900">{product.title}</h2>
								<Badge className="bg-green-100 text-green-800 border-green-200 text-xs font-medium mt-2">
									E-BOOK
								</Badge>
							</div>

							{product.description && (
								<p className="text-gray-600 text-sm leading-relaxed">
									{product.description}
								</p>
							)}
						</div>
					</div>

					{/* File information */}
					<div className="bg-gray-50 rounded-lg p-4 space-y-3">
						<h3 className="font-medium text-gray-900 flex items-center gap-2">
							<FileText className="h-4 w-4" />
							Informações do Arquivo
						</h3>

						{formats.length > 0 ? (
							<div className="space-y-2">
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">Formatos disponíveis:</span>
									<div className="flex gap-1">
										{formats.map((format) => (
											<Badge key={format} variant="outline" className="text-xs">
												{format.toUpperCase()}
											</Badge>
										))}
									</div>
								</div>

								<div className="text-xs text-gray-500">
									O arquivo está protegido com marca d'água e assinatura digital para sua segurança.
								</div>
							</div>
						) : (
							<div className="text-sm text-gray-500">
								Arquivo não disponível para download.
							</div>
						)}
					</div>

					{/* Security features */}
					<div className="bg-blue-50 rounded-lg p-4 space-y-3">
						<h3 className="font-medium text-gray-900 flex items-center gap-2">
							<Shield className="h-4 w-4" />
							Recursos de Segurança
						</h3>

						<div className="space-y-2">
							<div className="flex items-center gap-2 text-sm">
								<CheckCircle2 className="h-4 w-4 text-green-600" />
								<span>Marca d'água personalizada com seus dados</span>
							</div>
							<div className="flex items-center gap-2 text-sm">
								<CheckCircle2 className="h-4 w-4 text-green-600" />
								<span>Assinatura digital para autenticidade</span>
							</div>
							<div className="flex items-center gap-2 text-sm">
								<CheckCircle2 className="h-4 w-4 text-green-600" />
								<span>Controle de downloads (máximo 5 por compra)</span>
							</div>
							<div className="flex items-center gap-2 text-sm">
								<CheckCircle2 className="h-4 w-4 text-green-600" />
								<span>Rastreamento de IP para segurança</span>
							</div>
						</div>
					</div>

					{/* Download instructions */}
					<div className="bg-yellow-50 rounded-lg p-4 space-y-2">
						<h3 className="font-medium text-gray-900 flex items-center gap-2">
							<Info className="h-4 w-4" />
							Instruções de Download
						</h3>
						<ul className="text-sm text-gray-600 space-y-1">
							<li>• Clique em "Baixar E-book" para iniciar o download protegido</li>
							<li>• O arquivo será personalizado com suas informações</li>
							<li>• Você tem até 5 downloads por compra</li>
							<li>• O download abrirá em uma nova aba</li>
						</ul>
					</div>

					{/* Action buttons */}
					<div className="flex gap-3 pt-4">
						<Button
							onClick={onClose}
							variant="outline"
							className="flex-1"
						>
							Fechar
						</Button>
						{formats.length > 0 && (
							<Button
								onClick={onDownload}
								className="flex-1 bg-green-600 hover:bg-green-700"
							>
								<Download className="h-4 w-4 mr-2" />
								Baixar E-book
							</Button>
						)}
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
