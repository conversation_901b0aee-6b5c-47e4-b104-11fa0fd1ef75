'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import {
	Download,
	FileText,
	Calendar,
	Eye,
	Shield,
	Clock,
	BookOpen,
	ExternalLink,
} from 'lucide-react';
import { apiClient } from '@shared/lib/api-client';
import { toast } from 'sonner';
import { EbookDetailsModal } from './EbookDetailsModal';
import { EbookDownloadModal } from './EbookDownloadModal';

interface EbookCardProps {
	product: {
		id: string;
		title: string;
		description?: string | null;
		thumbnail?: string | null;
		ebook?: {
			id: string;
			fileUrl?: string | null;
			fileUrls?: any;
			format?: string[];
		} | null;
	};
	purchaseDate?: string;
	downloadCount?: number;
	maxDownloads?: number;
}

export function EbookCard({ product, purchaseDate, downloadCount = 0, maxDownloads = 5 }: EbookCardProps) {
	const [isDownloading, setIsDownloading] = useState(false);
	const [showDetailsModal, setShowDetailsModal] = useState(false);
	const [showDownloadModal, setShowDownloadModal] = useState(false);

	// Função utilitária para iniciar download (mesma lógica do certificado)
	const triggerDownload = async (downloadUrl: string, fileName: string) => {
		console.log('🔍 triggerDownload chamada:', { downloadUrl: downloadUrl.substring(0, 50) + '...', fileName });

		try {
			if (downloadUrl.startsWith('data:')) {
				console.log('🔍 Processando data URL');

				// Converter data URL para blob
				const response = await fetch(downloadUrl);
				const blob = await response.blob();
				const blobUrl = URL.createObjectURL(blob);

				console.log('🔍 Blob criado, iniciando download');

				// Criar link temporário
				const link = document.createElement('a');
				link.href = blobUrl;
				link.download = fileName || 'ebook.pdf';
				link.style.display = 'none';

				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				// Limpar o blob URL após um tempo
				setTimeout(() => {
					URL.revokeObjectURL(blobUrl);
					console.log('🔍 Blob URL limpo');
				}, 1000);

				console.log('🔍 Download iniciado via data URL');

			} else if (downloadUrl.startsWith('blob:')) {
				console.log('🔍 Processando blob URL');

				// Criar link temporário
				const link = document.createElement('a');
				link.href = downloadUrl;
				link.download = fileName || 'ebook.pdf';
				link.style.display = 'none';

				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				console.log('🔍 Download iniciado via blob URL');

			} else {
				console.log('🔍 Abrindo URL normal em nova aba');
				// Para URLs normais, abrir em nova aba
				window.open(downloadUrl, '_blank');
			}
		} catch (error) {
			console.error('🔍 Erro em triggerDownload:', error);
			// Fallback final: tentar abrir a URL diretamente
			window.open(downloadUrl, '_blank');
		}
	};

	// Download mutation
	const downloadEbook = apiClient.ebooks.download.useMutation({
		onSuccess: async (result) => {
			console.log('🔍 downloadEbook onSuccess:', result);

			if (result.downloadUrl) {
				// Gerar nome do arquivo baseado no produto
				const fileName = `ebook-${product.title}-${Date.now()}.pdf`;
				console.log('🔍 Chamando triggerDownload com:', {
					downloadUrl: result.downloadUrl.substring(0, 50) + '...',
					fileName
				});
				await triggerDownload(result.downloadUrl, fileName);
				toast.success('🔒 Download protegido iniciado!');
			} else {
				console.log('🔍 Sem URL de download');
				toast.error('URL de download não disponível');
			}
			setIsDownloading(false);
		},
		onError: (error) => {
			toast.error('Erro ao baixar o ebook protegido');
			console.error('Protected download error:', error);
			setIsDownloading(false);
		}
	});

	const handleQuickDownload = async () => {
		if (!product.ebook?.fileUrl && !product.ebook?.fileUrls) {
			toast.error('Arquivo não disponível para download');
			return;
		}

		setIsDownloading(true);
		try {
			await downloadEbook.mutateAsync({
				productId: product.id,
				format: 'PDF',
				includeWatermark: true,
				includeSignature: true,
				securityLevel: 'STANDARD'
			});
		} catch (error) {
			console.error('Download error:', error);
		}
	};

	const getFileFormats = () => {
		if (product.ebook?.fileUrls) {
			return Object.keys(product.ebook.fileUrls);
		}
		if (product.ebook?.fileUrl) {
			return ['PDF'];
		}
		return [];
	};

	const formats = getFileFormats();
	const remainingDownloads = maxDownloads - downloadCount;

	return (
		<>
			<Card className="group overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-[1.02] bg-white">
				<div className="relative">
					{product.thumbnail ? (
						<div
							className="h-48 bg-cover bg-center relative"
							style={{ backgroundImage: `url(${product.thumbnail})` }}
						>
							<div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-colors" />
						</div>
					) : (
						<div className="h-48 bg-gradient-to-br from-green-100 via-green-50 to-green-25 flex items-center justify-center relative">
							<div className="absolute inset-0 bg-gradient-to-br from-green-50 to-transparent" />
							<div className="relative z-10 p-4 text-green-600">
								<BookOpen className="h-12 w-12" />
							</div>
						</div>
					)}

					{/* Type Badge */}
					<div className="absolute top-3 left-3">
						<Badge className="bg-green-100 text-green-800 border-green-200 text-xs font-medium border">
							E-BOOK
						</Badge>
					</div>

					{/* Download Status Badge */}
					{remainingDownloads <= 2 && remainingDownloads > 0 && (
						<div className="absolute top-3 right-3">
							<Badge variant="destructive" className="text-xs">
								{remainingDownloads} downloads restantes
							</Badge>
						</div>
					)}
				</div>

				<CardHeader className="pb-3 p-4">
					<CardTitle className="text-lg line-clamp-2 leading-tight group-hover:text-green-600 transition-colors">
						{product.title}
					</CardTitle>
					{product.description && (
						<p className="text-sm text-gray-600 line-clamp-2 mt-2">
							{product.description}
						</p>
					)}
				</CardHeader>

				<CardContent className="space-y-4 p-4 pt-0">
					{/* Purchase date */}
					{purchaseDate && (
						<p className="text-xs text-gray-500 flex items-center gap-1">
							<Calendar className="h-3 w-3" />
							Adquirido em {new Date(purchaseDate).toLocaleDateString('pt-BR')}
						</p>
					)}

					{/* Download info */}
					<div className="flex items-center justify-between text-xs text-gray-500">
						<div className="flex items-center gap-1">
							<Download className="h-3 w-3" />
							<span>{downloadCount}/{maxDownloads} downloads</span>
						</div>
						{formats.length > 0 && (
							<div className="flex items-center gap-1">
								<FileText className="h-3 w-3" />
								<span>{formats.join(', ')}</span>
							</div>
						)}
					</div>

					{/* Action Buttons */}
					<div className="space-y-2">
						{formats.length > 0 ? (
							<>
								<Button
									onClick={() => setShowDetailsModal(true)}
									variant="outline"
									size="sm"
									className="w-full text-xs"
								>
									<Eye className="h-3 w-3 mr-2" />
									Ver Detalhes
								</Button>
								<Button
									onClick={() => setShowDownloadModal(true)}
									variant="default"
									size="sm"
									className="w-full text-xs bg-green-600 hover:bg-green-700"
									disabled={isDownloading || remainingDownloads <= 0}
								>
									<Download className="h-3 w-3 mr-2" />
									{isDownloading ? 'Baixando...' : 'Baixar E-book'}
								</Button>
							</>
						) : (
							<div className="text-center py-2">
								<p className="text-sm text-gray-500">Arquivo não disponível</p>
							</div>
						)}
					</div>

					{/* Quick download for single format */}
					{formats.length === 1 && formats[0] === 'PDF' && (
						<Button
							onClick={handleQuickDownload}
							variant="ghost"
							size="sm"
							className="w-full text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
							disabled={isDownloading || remainingDownloads <= 0}
						>
							<ExternalLink className="h-3 w-3 mr-1" />
							Download Rápido
						</Button>
					)}
				</CardContent>
			</Card>

			{/* Modals */}
			<EbookDetailsModal
				product={product}
				isOpen={showDetailsModal}
				onClose={() => setShowDetailsModal(false)}
				onDownload={() => {
					setShowDetailsModal(false);
					setShowDownloadModal(true);
				}}
			/>

			<EbookDownloadModal
				product={product}
				isOpen={showDownloadModal}
				onClose={() => setShowDownloadModal(false)}
				downloadCount={downloadCount}
				maxDownloads={maxDownloads}
			/>
		</>
	);
}
