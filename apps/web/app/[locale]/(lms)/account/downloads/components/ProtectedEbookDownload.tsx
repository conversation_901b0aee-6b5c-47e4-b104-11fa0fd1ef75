'use client';

import React, { useState } from 'react';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';
import {
  Shield,
  Download,
  Lock,
  RefreshCw,
  CheckCircle2,
  AlertCircle,
  Settings,
  Info
} from 'lucide-react';
import { apiClient } from '@shared/lib/api-client';
import { toast } from 'sonner';

interface ProtectedEbookDownloadProps {
  product: any;
  onDownloadStart?: () => void;
  onDownloadComplete?: () => void;
}

export function ProtectedEbookDownload({ 
  product, 
  onDownloadStart, 
  onDownloadComplete 
}: ProtectedEbookDownloadProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedSecurityLevel, setSelectedSecurityLevel] = useState<'BASIC' | 'STANDARD' | 'HIGH' | 'MAXIMUM'>('STANDARD');
  const [includeWatermark, setIncludeWatermark] = useState(true);
  const [includeSignature, setIncludeSignature] = useState(true);

  // Get download status for this specific ebook
  const { data: downloadStatus, refetch: refetchStatus, isLoading: statusLoading } = apiClient.ebooks.getDownloadStatus.useQuery({
    productId: product.id,
  });

  // Download mutation
  const downloadEbook = apiClient.ebooks.download.useMutation({
    onSuccess: (result) => {
      if (result.downloadUrl) {
        // Open download URL in new tab
        window.open(result.downloadUrl, '_blank');
        toast.success('🔒 Download protegido iniciado! O ebook foi protegido com suas informações.');
        onDownloadComplete?.();
      }
      refetchStatus();
    },
    onError: (error) => {
      toast.error(`Erro no download protegido: ${error.message}`);
    }
  });

  // Handle protected download
  const handleProtectedDownload = async () => {
    if (!downloadStatus?.canDownload) {
      toast.error(downloadStatus?.reason || 'Download não permitido');
      return;
    }

    setIsDownloading(true);
    onDownloadStart?.();

    try {
      await downloadEbook.mutateAsync({
        productId: product.id,
        includeWatermark,
        includeSignature,
        securityLevel: selectedSecurityLevel
      });
    } catch (error) {
      console.error('Protected download error:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Security level configurations
  const securityLevels = {
    BASIC: { label: 'Básico', color: 'bg-blue-100 text-blue-800', icon: '🔒' },
    STANDARD: { label: 'Padrão', color: 'bg-green-100 text-green-800', icon: '🛡️' },
    HIGH: { label: 'Alto', color: 'bg-orange-100 text-orange-800', icon: '🔐' },
    MAXIMUM: { label: 'Máximo', color: 'bg-red-100 text-red-800', icon: '🛡️⚡' }
  };

  // If loading status, show simple loading state
  if (statusLoading) {
    return (
      <div className="flex items-center gap-2">
        <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
        <span className="text-sm text-gray-500">Verificando...</span>
      </div>
    );
  }

  // If no purchase found, show purchase required message
  if (!downloadStatus?.hasPurchase) {
    return (
      <div className="flex items-center gap-2">
        <Lock className="h-4 w-4 text-gray-400" />
        <span className="text-sm text-gray-500">Compra necessária</span>
      </div>
    );
  }

  // If download not allowed, show reason
  if (!downloadStatus?.canDownload) {
    return (
      <div className="flex items-center gap-2">
        <AlertCircle className="h-4 w-4 text-red-500" />
        <span className="text-sm text-red-600">{downloadStatus.reason}</span>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Download Status and Progress */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium text-green-700">Pronto para download</span>
          <Badge className={securityLevels[selectedSecurityLevel].color}>
            {securityLevels[selectedSecurityLevel].icon} {securityLevels[selectedSecurityLevel].label}
          </Badge>
        </div>
        
        {downloadStatus.remainingDownloads !== undefined && (
          <div className="flex items-center gap-2">
            <Progress 
              value={(downloadStatus.downloadCount / downloadStatus.maxDownloads) * 100} 
              className="w-16 h-2"
            />
            <span className="text-xs text-gray-500">
              {downloadStatus.remainingDownloads} restantes
            </span>
          </div>
        )}
      </div>

      {/* Advanced Options Toggle */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-xs"
        >
          <Settings className="h-3 w-3 mr-1" />
          {showAdvanced ? 'Ocultar' : 'Configurar'} Proteção
        </Button>
        
        <Button
          onClick={handleProtectedDownload}
          disabled={isDownloading}
          size="sm"
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          {isDownloading ? (
            <>
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Protegendo...
            </>
          ) : (
            <>
              <Download className="h-3 w-3 mr-1" />
              Download Protegido
            </>
          )}
        </Button>
      </div>

      {/* Advanced Security Options */}
      {showAdvanced && (
        <div className="p-3 bg-gray-50 rounded-lg space-y-3 border">
          {/* Security Level Selection */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">
              Nível de Proteção
            </label>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(securityLevels).map(([level, info]) => (
                <button
                  key={level}
                  className={`p-2 text-xs border rounded-md transition-all text-left ${
                    selectedSecurityLevel === level
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedSecurityLevel(level as any)}
                >
                  <div className="flex items-center gap-1">
                    <span>{info.icon}</span>
                    <span className="font-medium">{info.label}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Protection Options */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-gray-700">Opções de Proteção</label>
            <div className="space-y-1">
              <label className="flex items-center gap-2 text-xs">
                <input
                  type="checkbox"
                  checked={includeWatermark}
                  onChange={(e) => setIncludeWatermark(e.target.checked)}
                  className="w-3 h-3 rounded"
                />
                <span>Marca d'água com suas informações</span>
              </label>
              
              <label className="flex items-center gap-2 text-xs">
                <input
                  type="checkbox"
                  checked={includeSignature}
                  onChange={(e) => setIncludeSignature(e.target.checked)}
                  className="w-3 h-3 rounded"
                />
                <span>Assinatura digital de autenticidade</span>
              </label>
            </div>
          </div>

          {/* Security Info */}
          <div className="flex items-start gap-2 p-2 bg-blue-50 rounded border border-blue-200">
            <Info className="h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0" />
            <p className="text-xs text-blue-700">
              Este ebook será protegido com suas informações pessoais e assinatura digital 
              para combater a pirataria e garantir autenticidade.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}