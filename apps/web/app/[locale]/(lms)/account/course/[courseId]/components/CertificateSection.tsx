'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';
import {
  Award,
  CheckCircle,
  Download,
  FileText,
  AlertCircle,
  GraduationCap,
  Sparkles,
  RefreshCw,
  Clock
} from 'lucide-react';
import { apiClient } from '@shared/lib/api-client';
import { toast } from 'sonner';

interface CertificateSectionProps {
  courseId: string;
  course: any;
  enrollment: any;
}

export function CertificateSection({ courseId, course, enrollment }: CertificateSectionProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  // Função utilitária para iniciar download
  const triggerDownload = async (downloadUrl: string, fileName: string) => {
    console.log('🔍 triggerDownload chamada:', { downloadUrl: downloadUrl.substring(0, 50) + '...', fileName });

    try {
      if (downloadUrl.startsWith('data:')) {
        console.log('🔍 Processando data URL');

        // Converter data URL para blob
        const response = await fetch(downloadUrl);
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        console.log('🔍 Blob criado, iniciando download');

        // Criar link temporário
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName || 'certificado.pdf';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Limpar o blob URL após um tempo
        setTimeout(() => {
          URL.revokeObjectURL(blobUrl);
          console.log('🔍 Blob URL limpo');
        }, 1000);

        console.log('🔍 Download iniciado via data URL');

      } else if (downloadUrl.startsWith('blob:')) {
        console.log('🔍 Processando blob URL');

        // Criar link temporário
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName || 'certificado.pdf';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('🔍 Download iniciado via blob URL');

      } else {
        console.log('🔍 Abrindo URL normal em nova aba');
        // Para URLs normais, abrir em nova aba
        window.open(downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('🔍 Erro em triggerDownload:', error);
      // Fallback final: tentar abrir a URL diretamente
      window.open(downloadUrl, '_blank');
    }
  };

  // Debug logs
  console.log('🔍 CertificateSection renderizado com:', { courseId, course, enrollment });
  console.log('🔍 Course completo:', course);
  console.log('🔍 Course certificateEnabled:', course?.certificateEnabled);
  console.log('🔍 Course certificateEnabled type:', typeof course?.certificateEnabled);
  console.log('🔍 Enrollment completedAt:', enrollment?.completedAt);

  // Verificar se o curso tem certificado habilitado
  const isCertificateEnabled = Boolean(course?.certificateEnabled);

  // Calcular progresso usando os dados da API (mais confiável)
  const calculateProgress = () => {
    if (!course?.modules) return 0;

    const totalLessons = course.modules.reduce(
      (acc: number, module: any) => acc + module.lessons.length, 0
    );

    const completedLessons = course.modules.reduce(
      (acc: number, module: any) =>
        acc + module.lessons.filter((lesson: any) => lesson.completed).length, 0
    );

    return totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
  };

  const currentProgress = calculateProgress();

  // Verificar se o aluno concluiu o curso baseado no progresso das aulas
  // Usar os dados da API em vez do enrollment.progress que pode estar desatualizado
  const isCourseCompleted = currentProgress >= 100;

  console.log('🔍 Valores computados:', {
    isCertificateEnabled,
    isCourseCompleted,
    currentProgress,
    totalLessons: course?.modules?.reduce((acc: number, module: any) => acc + module.lessons.length, 0) || 0,
    completedLessons: course?.modules?.reduce((acc: number, module: any) =>
      acc + module.lessons.filter((lesson: any) => lesson.completed).length, 0
    ) || 0
  });

  // Verificar se já existe um certificado
  const { data: existingCertificate, refetch, error: certificateError, isLoading: certificateLoading } = apiClient.certificates.getByCourse.useQuery({
    courseId,
  }, {
    enabled: Boolean(isCertificateEnabled)
  });

  // Debug logs para API
  console.log('🔍 API getByCourse resultado:', {
    existingCertificate,
    certificateError,
    certificateLoading,
    enabled: Boolean(isCourseCompleted && isCertificateEnabled)
  });

  // Mutação para gerar certificado
  const generateCertificate = apiClient.certificates.generate.useMutation({
    onSuccess: () => {
      toast.success('Certificado gerado com sucesso!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Erro ao gerar certificado: ${error.message}`);
    }
  });

  // Função para gerar certificado (simplificada como ebooks)
  const handleGenerateCertificate = async () => {
    if (!isCourseCompleted) {
      toast.error('Você precisa concluir o curso para gerar o certificado');
      return;
    }

    setIsGenerating(true);
    try {
      // Se já existe certificado, apenas baixar
      if (existingCertificate) {
        const dl = await downloadCertificate.mutateAsync({
          certificateId: existingCertificate.id
        });
        if (dl.downloadUrl) {
          await triggerDownload(dl.downloadUrl, dl.fileName || 'certificado.pdf');
          toast.success('Download iniciado!');
        }
        return;
      }

      // Criar certificado e baixar em uma operação (agora retorna data URL diretamente)
      const result = await apiClient.certificates.generate.mutate({
        courseId,
        studentName: enrollment.user?.name
      });

      if (result?.downloadUrl) {
        // Baixar imediatamente usando a data URL retornada
        await triggerDownload(result.downloadUrl, result.fileName || 'certificado.pdf');
        toast.success('Certificado gerado e download iniciado!');
      } else if (result?.id) {
        // Fallback: se não retornou data URL, usar o ID para download
        const dl = await downloadCertificate.mutateAsync({
          certificateId: result.id
        });
        if (dl.downloadUrl) {
          await triggerDownload(dl.downloadUrl, dl.fileName || 'certificado.pdf');
          toast.success('Certificado gerado e download iniciado!');
        }
      }

      await refetch();
    } catch (e: any) {
      toast.error(e?.message || 'Erro ao gerar o certificado');
    } finally {
      setIsGenerating(false);
    }
  };

  // Mutação para download de certificado
  const downloadCertificate = apiClient.certificates.download.useMutation({
    onSuccess: async (result) => {
      console.log('🔍 downloadCertificate onSuccess:', result);

      if (result.downloadUrl) {
        console.log('🔍 Chamando triggerDownload com:', {
          downloadUrl: result.downloadUrl,
          fileName: result.fileName
        });
        await triggerDownload(result.downloadUrl, result.fileName || 'certificado.pdf');
        toast.success('Download iniciado!');
      } else {
        console.log('🔍 Sem URL de download');
        toast.error('URL de download não disponível');
      }
    },
    onError: (error) => {
      console.error('Erro ao baixar certificado:', error);
      toast.error(`Erro ao baixar certificado: ${error.message || 'Erro desconhecido'}`);
    }
  });

  // Função para baixar certificado
  const handleDownloadCertificate = async () => {
    if (!existingCertificate?.id) {
      toast.error('ID do certificado não disponível');
      return;
    }

    setIsDownloading(true);
    try {
      await downloadCertificate.mutateAsync({
        certificateId: existingCertificate.id
      });
    } finally {
      setIsDownloading(false);
    }
  };

  // Se o curso não tem certificado habilitado
  if (!isCertificateEnabled) {
    return (
      <Card className="bg-gray-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Award className="w-5 h-5 text-gray-400" />
            Certificado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <GraduationCap className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">
              Este curso não oferece certificado de conclusão.
            </p>
            <p className="text-xs text-gray-400 mt-2">
              O instrutor pode habilitar certificados nas configurações do curso.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Se o curso não foi concluído
  if (!isCourseCompleted) {
    return (
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-blue-800">
            <Award className="w-5 h-5" />
            Certificado Disponível
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Clock className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-800">
                  Aguardando conclusão do curso
                </p>
                <p className="text-xs text-blue-600">
                  Complete todas as aulas para gerar seu certificado
                </p>
              </div>
            </div>

            {/* Mostrar progresso atual */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-blue-700">
                <span>Progresso atual</span>
                <span>{currentProgress}%</span>
              </div>
              <Progress
                value={currentProgress}
                className="h-2"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Se o curso foi concluído mas não tem certificado
  if (!existingCertificate) {
    return (
      <Card className="bg-green-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-green-800">
            <Award className="w-5 h-5" />
            Certificado Disponível
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-full">
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-green-800">
                  Parabéns! Você concluiu o curso
                </p>
                <p className="text-xs text-green-600">
                  Gere seu certificado de conclusão agora
                </p>
              </div>
            </div>

            <Button
              onClick={handleGenerateCertificate}
              disabled={isGenerating}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                  Gerando Certificado...
                </>
              ) : (
                <>
                  <Award className="w-4 h-4 mr-2" />
                  Gerar Certificado
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Se já existe certificado
  if (existingCertificate) {
    console.log('🔍 Renderizando: Certificado já existe');

    // Verificar se o certificado tem PDF
    const hasPDF = existingCertificate.pdfUrl;

    return (
      <Card className="bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-purple-800">
            <Award className="w-5 h-5" />
            {hasPDF ? 'Certificado Gerado' : 'Certificado Pendente'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-full">
                {hasPDF ? (
                  <CheckCircle className="w-4 h-4 text-purple-600" />
                ) : (
                  <Clock className="w-4 h-4 text-orange-600" />
                )}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-purple-800">
                  {hasPDF ? 'Seu certificado está pronto!' : 'Seu certificado está sendo processado'}
                </p>
                <p className="text-xs text-purple-600">
                  {hasPDF ? 'Baixe e compartilhe sua conquista' : 'Aguarde alguns instantes para o PDF ficar disponível'}
                </p>
              </div>
            </div>

            {/* Informações do certificado */}
            <div className="bg-white/50 rounded-lg p-3 space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-gray-600">Número:</span>
                <span className="font-mono text-gray-800">
                  {existingCertificate.certificateNumber?.slice(0, 8) || 'N/A'}...
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-600">Código de Validação:</span>
                <span className="font-mono text-gray-800">
                  {existingCertificate.validationCode || 'N/A'}
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-600">Data de Emissão:</span>
                <span className="text-gray-800">
                  {existingCertificate.issueDate ? new Date(existingCertificate.issueDate).toLocaleDateString('pt-BR') : 'N/A'}
                </span>
              </div>
            </div>

            {/* Botões de ação */}
            <div className="flex gap-2">
              {hasPDF ? (
                <Button
                  onClick={handleDownloadCertificate}
                  disabled={isDownloading}
                  className="flex-1 bg-purple-600 hover:bg-purple-700"
                >
                  {isDownloading ? (
                    <>
                      <Download className="w-4 h-4 mr-2 animate-bounce" />
                      Baixando...
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4 mr-2" />
                      Baixar PDF
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleGenerateCertificate}
                  disabled={isGenerating}
                  className="flex-1 bg-orange-600 hover:bg-orange-700"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Gerando PDF...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Gerar PDF
                    </>
                  )}
                </Button>
              )}

              <Button
                variant="outline"
                onClick={() => {
                  if (existingCertificate.validationCode) {
                    navigator.clipboard.writeText(existingCertificate.validationCode);
                    toast.success('Código copiado para a área de transferência!');
                  }
                }}
                className="px-3"
                disabled={!existingCertificate.validationCode}
              >
                <FileText className="w-4 h-4" />
              </Button>
            </div>

            {/* Status do certificado */}
            <div className="flex items-center justify-center">
              <Badge className={`text-xs ${hasPDF ? 'bg-green-100 text-green-800 border-green-200' : 'bg-orange-100 text-orange-800 border-orange-200'}`}>
                {hasPDF ? 'Ativo' : 'Processando PDF'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Se chegou até aqui, o curso foi concluído mas não tem certificado
  console.log('🔍 Renderizando: Curso concluído, sem certificado - mostrar botão de geração');
  return (
    <Card className="bg-green-50 border-green-200">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2 text-green-800">
          <Award className="w-5 h-5" />
          Gerar Certificado
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-full">
              <CheckCircle className="w-4 h-4 text-green-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-green-800">
                Parabéns! Você concluiu o curso
              </p>
              <p className="text-xs text-green-600">
                Clique no botão abaixo para gerar seu certificado
              </p>
            </div>
          </div>

          {/* Botão de geração */}
          <Button
            onClick={handleGenerateCertificate}
            disabled={isGenerating}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            {isGenerating ? (
              <>
                <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" />
                Gerar Certificado
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
