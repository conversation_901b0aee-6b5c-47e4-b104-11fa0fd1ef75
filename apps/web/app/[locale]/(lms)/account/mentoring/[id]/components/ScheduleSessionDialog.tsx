'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { <PERSON>alog, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@ui/components/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@ui/components/form';
import { Input } from '@ui/components/input';
import { Textarea } from '@ui/components/textarea';
import { Button } from '@ui/components/button';
import { Calendar, Clock, Plus } from 'lucide-react';
import { useToast } from '@ui/hooks/use-toast';
import { apiClient } from '@shared/lib/api-client';

const scheduleSchema = z.object({
	scheduledAt: z.string().min(1, 'Data e hora são obrigatórias'),
	duration: z.number().min(15).max(480),
	notes: z.string().optional(),
});

type ScheduleFormValues = z.infer<typeof scheduleSchema>;

interface ScheduleSessionDialogProps {
	mentoringId: string;
	onSuccess?: () => void;
}

export function ScheduleSessionDialog({ mentoringId, onSuccess }: ScheduleSessionDialogProps) {
	const { toast } = useToast();
	const [isOpen, setIsOpen] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const scheduleMutation = apiClient.products.scheduleMentoringSession.useMutation();

	const form = useForm<ScheduleFormValues>({
		resolver: zodResolver(scheduleSchema),
		defaultValues: {
			scheduledAt: '',
			duration: 60,
			notes: '',
		},
	});

	const onSubmit = async (data: ScheduleFormValues) => {
		setIsSubmitting(true);
		try {
			await scheduleMutation.mutateAsync({
				mentoringId,
				...data,
			});

			toast({
				title: 'Sessão agendada',
				description: 'Sua sessão foi agendada com sucesso!',
			});

			setIsOpen(false);
			form.reset();
			onSuccess?.();
		} catch (error) {
			toast({
				title: 'Erro ao agendar',
				description: (error as Error).message || 'Erro ao agendar sessão',
				variant: 'error',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleOpenChange = (open: boolean) => {
		setIsOpen(open);
		if (!open) {
			form.reset();
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleOpenChange}>
			<DialogTrigger asChild>
				<Button>
					<Plus className="mr-2 h-4 w-4" />
					Agendar Sessão
				</Button>
			</DialogTrigger>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>Agendar Nova Sessão</DialogTitle>
				</DialogHeader>
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="scheduledAt"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-2">
										<Calendar className="h-4 w-4" />
										Data e Hora
									</FormLabel>
									<FormControl>
										<Input
											type="datetime-local"
											{...field}
											min={new Date().toISOString().slice(0, 16)}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="duration"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-2">
										<Clock className="h-4 w-4" />
										Duração (minutos)
									</FormLabel>
									<FormControl>
										<Input
											type="number"
											min="15"
											max="480"
											{...field}
											onChange={(e) => field.onChange(+e.target.value)}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="notes"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Observações</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Alguma observação ou objetivo para esta sessão?"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="flex justify-end gap-3">
							<Button
								type="button"
								variant="outline"
								onClick={() => setIsOpen(false)}
							>
								Cancelar
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? 'Agendando...' : 'Agendar Sessão'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
