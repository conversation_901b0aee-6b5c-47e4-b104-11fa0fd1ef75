'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@shared/lib/api-client';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/tabs';
import {
	Calendar,
	Clock,
	Video,
	MapPin,
	Users,
	BookOpen,
	FileText,
	ArrowLeft,
	Plus,
	Play,
	Link2Icon
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ScheduleSessionDialog } from './components/ScheduleSessionDialog';

interface MentoringPageClientProps {
	mentoringId: string;
}

export function MentoringPageClient({ mentoringId }: MentoringPageClientProps) {
	const router = useRouter();
	const [activeTab, setActiveTab] = useState('overview');

	// Buscar dados da mentoria
	const { data: mentoring, isLoading } = apiClient.products.getMentoringById.useQuery(
		{ mentoringId },
		{ enabled: !!mentoringId }
	);

	// Buscar sessões da mentoria
	const { data: sessions, isLoading: sessionsLoading, refetch: refetchSessions } = apiClient.products.getMentoringSessions.useQuery(
		{ mentoringId },
		{ enabled: !!mentoringId }
	);

	// Buscar recursos da mentoria
	const { data: resources, isLoading: resourcesLoading } = apiClient.products.getMentoringResources.useQuery(
		{ mentoringId },
		{ enabled: !!mentoringId }
	);

	if (isLoading) {
		return <div>Carregando...</div>;
	}

	if (!mentoring) {
		return (
			<div className="container py-8">
				<Card className="p-6">
					<h2 className="text-xl font-semibold mb-2">Mentoria não encontrada</h2>
					<p className="text-muted-foreground mb-4">
						Não foi possível carregar os dados da mentoria.
					</p>
					<Button asChild variant="default">
						<Link href="/account">
							<ArrowLeft className="mr-2 h-4 w-4" />
							Voltar para minha conta
						</Link>
					</Button>
				</Card>
			</div>
		);
	}

	const nextSession = sessions?.find(session =>
		session.status === 'SCHEDULED' &&
		new Date(session.scheduledAt) > new Date()
	);

	const completedSessions = sessions?.filter(session =>
		session.status === 'COMPLETED'
	) || [];

	return (
		<div className="container py-8 space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div className="space-y-2">
					<Button variant="ghost" size="sm" asChild>
						<Link href="/account">
							<ArrowLeft className="mr-2 h-4 w-4" />
							Minha Conta
						</Link>
					</Button>
					<h1 className="text-3xl font-bold">{mentoring.product?.title}</h1>
					<p className="text-muted-foreground">
						Mentoria com {mentoring.product?.creator?.name}
					</p>
				</div>
				<div className="flex items-center gap-3">
					<Badge variant="secondary">
						{mentoring.duration} min
					</Badge>
					<Badge variant="outline">
						{mentoring.location}
					</Badge>
				</div>
			</div>

			{/* Main content */}
			<Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
				<TabsList className="grid w-full grid-cols-4">
					<TabsTrigger value="overview">Visão Geral</TabsTrigger>
					<TabsTrigger value="sessions">Sessões</TabsTrigger>
					<TabsTrigger value="content">Conteúdo</TabsTrigger>
					<TabsTrigger value="resources">Recursos</TabsTrigger>
				</TabsList>

				{/* Overview Tab */}
				<TabsContent value="overview" className="space-y-6">
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
						{/* Left column - Next session and content */}
						<div className="lg:col-span-2 space-y-6">
							{/* Next session */}
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<Calendar className="h-5 w-5" />
										Próxima Sessão
									</CardTitle>
								</CardHeader>
								<CardContent>
									{nextSession ? (
										<div className="space-y-4">
											<div className="flex items-center justify-between">
												<div>
													<p className="font-medium">
														{format(new Date(nextSession.scheduledAt), "EEEE, d 'de' MMMM 'às' HH:mm", { locale: ptBR })}
													</p>
													<p className="text-sm text-muted-foreground">
														Duração: {nextSession.duration} minutos
													</p>
												</div>
												<Button size="sm">
													<Video className="mr-2 h-4 w-4" />
													Entrar na Reunião
												</Button>
											</div>
											{nextSession.meetingLink && (
												<div className="flex items-center gap-2 text-sm text-muted-foreground">
													<Link2Icon className="h-4 w-4" />
													<a
														href={nextSession.meetingLink}
														target="_blank"
														rel="noopener noreferrer"
														className="text-primary hover:underline"
													>
														{nextSession.meetingLink}
													</a>
												</div>
											)}
										</div>
									) : (
										<div className="text-center py-8">
											<Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
											<p className="text-muted-foreground mb-4">
												Nenhuma sessão agendada
											</p>
											<ScheduleSessionDialog
												mentoringId={mentoring.id}
												onSuccess={refetchSessions}
											/>
										</div>
									)}
								</CardContent>
							</Card>

							{/* Recent content */}
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<BookOpen className="h-5 w-5" />
										Conteúdo Recente
									</CardTitle>
								</CardHeader>
								<CardContent>
									{resources && resources.length > 0 ? (
										<div className="space-y-3">
											{resources.slice(0, 3).map((resource) => (
												<div key={resource.id} className="flex items-center justify-between p-3 border rounded-lg">
													<div className="flex items-center gap-3">
														<FileText className="h-5 w-5 text-muted-foreground" />
														<div>
															<p className="font-medium">{resource.title}</p>
															<p className="text-sm text-muted-foreground">
																{resource.type} • {resource.createdAt}
															</p>
														</div>
													</div>
													<Button size="sm" variant="outline">
														<Play className="mr-2 h-4 w-4" />
														Acessar
													</Button>
												</div>
											))}
										</div>
									) : (
										<div className="text-center py-8 text-muted-foreground">
											<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
											<p>Nenhum conteúdo disponível ainda</p>
										</div>
									)}
								</CardContent>
							</Card>
						</div>

						{/* Right column - Info and actions */}
						<div className="space-y-6">
							{/* Mentoring info */}
							<Card>
								<CardHeader>
									<CardTitle>Informações da Mentoria</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="flex items-center gap-3">
										<Clock className="h-4 w-4 text-muted-foreground" />
										<span className="text-sm">
											Duração: {mentoring.duration} minutos
										</span>
									</div>
									<div className="flex items-center gap-3">
										<MapPin className="h-4 w-4 text-muted-foreground" />
										<span className="text-sm">
											Local: {mentoring.location}
										</span>
									</div>
									<div className="flex items-center gap-3">
										<Users className="h-4 w-4 text-muted-foreground" />
										<span className="text-sm">
											Máximo: {mentoring.maxStudents} aluno(s)
										</span>
									</div>
									<div className="flex items-center gap-3">
										<Calendar className="h-4 w-4 text-muted-foreground" />
										<span className="text-sm">
											Fuso: {mentoring.timezone}
										</span>
									</div>
								</CardContent>
							</Card>

							{/* Quick actions */}
							<Card>
								<CardHeader>
									<CardTitle>Ações Rápidas</CardTitle>
								</CardHeader>
								<CardContent className="space-y-3">
									<ScheduleSessionDialog
										mentoringId={mentoring.id}
										onSuccess={refetchSessions}
									/>
									<Button variant="outline" className="w-full" size="sm">
										<Video className="mr-2 h-4 w-4" />
										Reagendar
									</Button>
									<Button variant="outline" className="w-full" size="sm">
										<FileText className="mr-2 h-4 w-4" />
										Solicitar Material
									</Button>
								</CardContent>
							</Card>

							{/* Progress */}
							<Card>
								<CardHeader>
									<CardTitle>Progresso</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="space-y-3">
										<div className="flex justify-between text-sm">
											<span>Sessões Concluídas</span>
											<span className="font-medium">{completedSessions.length}</span>
										</div>
										<div className="w-full bg-muted rounded-full h-2">
											<div
												className="bg-primary h-2 rounded-full transition-all"
												style={{
													width: `${Math.min((completedSessions.length / Math.max(sessions?.length || 1, 1)) * 100, 100)}%`
												}}
											/>
										</div>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</TabsContent>

				{/* Sessions Tab */}
				<TabsContent value="sessions" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center justify-between">
								<span>Histórico de Sessões</span>
								<ScheduleSessionDialog
									mentoringId={mentoring.id}
									onSuccess={refetchSessions}
								/>
							</CardTitle>
						</CardHeader>
						<CardContent>
							{sessions && sessions.length > 0 ? (
								<div className="space-y-4">
									{sessions.map((session) => (
										<div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
											<div className="flex items-center gap-4">
												<div className="text-center">
													<div className="text-2xl font-bold text-primary">
														{format(new Date(session.scheduledAt), 'dd')}
													</div>
													<div className="text-sm text-muted-foreground">
														{format(new Date(session.scheduledAt), 'MMM', { locale: ptBR })}
													</div>
												</div>
												<div>
													<p className="font-medium">
														{format(new Date(session.scheduledAt), "EEEE, d 'de' MMMM 'às' HH:mm", { locale: ptBR })}
													</p>
													<p className="text-sm text-muted-foreground">
														Duração: {session.duration} minutos
													</p>
												</div>
											</div>
											<div className="flex items-center gap-2">
												<Badge variant={
													session.status === 'COMPLETED' ? 'default' :
													session.status === 'SCHEDULED' ? 'secondary' :
													session.status === 'CANCELLED' ? 'destructive' :
													'outline'
												}>
													{session.status === 'COMPLETED' ? 'Concluída' :
													 session.status === 'SCHEDULED' ? 'Agendada' :
													 session.status === 'CANCELLED' ? 'Cancelada' :
													 session.status}
												</Badge>
												{session.status === 'SCHEDULED' && (
													<Button size="sm" variant="outline">
														<Video className="mr-2 h-4 w-4" />
														Entrar
													</Button>
												)}
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
									<p>Nenhuma sessão encontrada</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				{/* Content Tab */}
				<TabsContent value="content" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Conteúdo da Mentoria</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-center py-8 text-muted-foreground">
								<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
								<p>Conteúdo será disponibilizado pelo mentor</p>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				{/* Resources Tab */}
				<TabsContent value="resources" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Recursos e Materiais</CardTitle>
						</CardHeader>
						<CardContent>
							{resources && resources.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
									{resources.map((resource) => (
										<div key={resource.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
											<div className="flex items-start justify-between mb-3">
												<FileText className="h-8 w-8 text-primary" />
												<Badge variant="outline">{resource.type}</Badge>
											</div>
											<h3 className="font-medium mb-2">{resource.title}</h3>
											<p className="text-sm text-muted-foreground mb-3 line-clamp-2">
												{resource.description}
											</p>
											<Button className="w-full" size="sm">
												<Play className="mr-2 h-4 w-4" />
												Acessar
											</Button>
										</div>
									))}
								</div>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
									<p>Nenhum recurso disponível ainda</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
