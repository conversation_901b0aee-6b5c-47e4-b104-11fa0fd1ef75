import { Suspense } from 'react';
import { MentoringPageClient } from './MentoringPageClient';
import { MentoringPageSkeleton } from './MentoringPageSkeleton';

interface MentoringPageProps {
	params: {
		id: string;
	};
}

export default function MentoringPage({ params }: MentoringPageProps) {
	return (
		<Suspense fallback={<MentoringPageSkeleton />}>
			<MentoringPageClient mentoringId={params.id} />
		</Suspense>
	);
}
