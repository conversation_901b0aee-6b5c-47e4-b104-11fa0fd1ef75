import { Card } from '@ui/components/card';
import { Skeleton } from '@ui/components/skeleton';

export function MentoringPageSkeleton() {
	return (
		<div className="container py-8 space-y-6">
			{/* Header */}
			<div className="space-y-4">
				<Skeleton className="h-8 w-64" />
				<Skeleton className="h-4 w-96" />
			</div>

			{/* Main content */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Left column - Sessions and content */}
				<div className="lg:col-span-2 space-y-6">
					{/* Next session */}
					<Card className="p-6">
						<Skeleton className="h-6 w-48 mb-4" />
						<div className="space-y-3">
							<Skeleton className="h-4 w-full" />
							<Skeleton className="h-4 w-3/4" />
							<Skeleton className="h-10 w-32" />
						</div>
					</Card>

					{/* Content and resources */}
					<Card className="p-6">
						<Skeleton className="h-6 w-32 mb-4" />
						<div className="space-y-3">
							<Skeleton className="h-20 w-full" />
							<Skeleton className="h-20 w-full" />
						</div>
					</Card>
				</div>

				{/* Right column - Info and actions */}
				<div className="space-y-6">
					{/* Mentoring info */}
					<Card className="p-6">
						<Skeleton className="h-6 w-40 mb-4" />
						<div className="space-y-3">
							<Skeleton className="h-4 w-full" />
							<Skeleton className="h-4 w-3/4" />
							<Skeleton className="h-4 w-1/2" />
						</div>
					</Card>

					{/* Quick actions */}
					<Card className="p-6">
						<Skeleton className="h-6 w-32 mb-4" />
						<div className="space-y-3">
							<Skeleton className="h-10 w-full" />
							<Skeleton className="h-10 w-full" />
						</div>
					</Card>
				</div>
			</div>
		</div>
	);
}
