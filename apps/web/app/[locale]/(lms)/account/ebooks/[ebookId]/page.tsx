import { redirect } from "next/navigation";
import { apiClient } from "@shared/lib/api-client";
import { EbookDownloadSection } from "./components/EbookDownloadSection";

interface EbookPageProps {
  params: {
    ebookId: string;
    locale: string;
  };
}

export default async function EbookPage({ params }: EbookPageProps) {
  // In a real app, you'd get the user from session/auth
  // For now, we'll assume the user is authenticated
  
  try {
    // Get product/ebook data
    const product = await apiClient.products.getBySlug.query({
      slug: params.ebookId
    });

    if (!product || product.type !== "EBOOK") {
      redirect("/account");
    }

    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-16 h-20 bg-gray-200 rounded flex items-center justify-center">
                {product.thumbnail ? (
                  <img 
                    src={product.thumbnail} 
                    alt={product.title}
                    className="w-full h-full object-cover rounded"
                  />
                ) : (
                  <span className="text-2xl">📖</span>
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold">{product.title}</h1>
                <p className="text-gray-600">{product.shortDescription}</p>
                {product.creator && (
                  <p className="text-sm text-gray-500 mt-1">
                    Por {product.creator.name}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Download Section */}
          <EbookDownloadSection 
            productId={product.id} 
            product={product}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error loading ebook page:", error);
    redirect("/account");
  }
}