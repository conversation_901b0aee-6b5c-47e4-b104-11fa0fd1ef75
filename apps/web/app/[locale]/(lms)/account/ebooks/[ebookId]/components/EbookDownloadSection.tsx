'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';
import {
  BookOpen,
  Shield,
  Download,
  FileText,
  AlertCircle,
  CheckCircle2,
  Lock,
  RefreshCw,
  Clock,
  Eye,
  Settings
} from 'lucide-react';
import { apiClient } from '@shared/lib/api-client';
import { toast } from 'sonner';

interface EbookDownloadSectionProps {
  productId: string;
  product: any;
}

export function EbookDownloadSection({ productId, product }: EbookDownloadSectionProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedSecurityLevel, setSelectedSecurityLevel] = useState<'BASIC' | 'STANDARD' | 'HIGH' | 'MAXIMUM'>('STANDARD');
  const [includeWatermark, setIncludeWatermark] = useState(true);
  const [includeSignature, setIncludeSignature] = useState(true);

  console.log('🔍 EbookDownloadSection rendered with:', { productId, product });

  // Get download status
  const { data: downloadStatus, refetch: refetchStatus, isLoading: statusLoading } = apiClient.ebooks.getDownloadStatus.useQuery({
    productId,
  });

  // Get download history
  const { data: downloadHistory, refetch: refetchHistory } = apiClient.ebooks.getDownloadHistory.useQuery({
    productId,
    limit: 5
  });

  console.log('🔍 Download status:', downloadStatus);
  console.log('🔍 Download history:', downloadHistory);

  // Download mutation
  const downloadEbook = apiClient.ebooks.download.useMutation({
    onSuccess: (result) => {
      if (result.downloadUrl) {
        // Open download URL in new tab
        window.open(result.downloadUrl, '_blank');
        toast.success('Download iniciado! O ebook foi protegido com suas informações.');
      }
      refetchStatus();
      refetchHistory();
    },
    onError: (error) => {
      toast.error(`Erro ao baixar ebook: ${error.message}`);
    }
  });

  // Handle download
  const handleDownload = async () => {
    if (!downloadStatus?.canDownload) {
      toast.error(downloadStatus?.reason || 'Download não permitido');
      return;
    }

    setIsDownloading(true);
    try {
      await downloadEbook.mutateAsync({
        productId,
        includeWatermark,
        includeSignature,
        securityLevel: selectedSecurityLevel
      });
    } catch (error) {
      console.error('Download error:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Security level descriptions
  const securityLevels = {
    BASIC: {
      label: 'Básico',
      description: 'Marca d\'água com suas informações',
      features: ['Marca d\'água personalizada']
    },
    STANDARD: {
      label: 'Padrão',
      description: 'Marca d\'água + proteções básicas',
      features: ['Marca d\'água personalizada', 'Proteção contra cópia', 'Rastreamento de acesso']
    },
    HIGH: {
      label: 'Alto',
      description: 'Máxima proteção + assinatura digital',
      features: ['Marca d\'água personalizada', 'Assinatura digital', 'Proteção contra cópia/impressão', 'Rastreamento completo']
    },
    MAXIMUM: {
      label: 'Máximo',
      description: 'Proteção empresarial completa',
      features: ['Todas as proteções anteriores', 'Criptografia avançada', 'Validação pública', 'Relatórios de uso']
    }
  };

  // Render security level badge
  const getSecurityBadge = (level: string) => {
    const colors = {
      BASIC: 'bg-blue-100 text-blue-800',
      STANDARD: 'bg-green-100 text-green-800',
      HIGH: 'bg-orange-100 text-orange-800',
      MAXIMUM: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[level as keyof typeof colors]}>{securityLevels[level as keyof typeof securityLevels]?.label}</Badge>;
  };

  if (statusLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Download do Ebook
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Carregando...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!downloadStatus?.hasPurchase) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Download do Ebook
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Lock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 mb-4">Você precisa comprar este ebook para fazer o download.</p>
            <Button variant="default">
              Comprar Ebook
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Download Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Download Protegido do Ebook
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Download Status */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              {downloadStatus?.canDownload ? (
                <>
                  <CheckCircle2 className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">Pronto para download</p>
                    <p className="text-sm text-gray-600">
                      {downloadStatus.remainingDownloads} downloads restantes de {downloadStatus.maxDownloads}
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <div>
                    <p className="font-medium text-red-600">Download não disponível</p>
                    <p className="text-sm text-gray-600">{downloadStatus?.reason}</p>
                  </div>
                </>
              )}
            </div>
            
            {downloadStatus?.remainingDownloads !== undefined && (
              <div className="text-right">
                <Progress 
                  value={(downloadStatus.downloadCount / downloadStatus.maxDownloads) * 100} 
                  className="w-24"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {downloadStatus.downloadCount}/{downloadStatus.maxDownloads}
                </p>
              </div>
            )}
          </div>

          {/* Security Level Selection */}
          {downloadStatus?.canDownload && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <h4 className="font-medium">Nível de Proteção</h4>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {Object.entries(securityLevels).map(([level, info]) => (
                  <div
                    key={level}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedSecurityLevel === level
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedSecurityLevel(level as any)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">{info.label}</span>
                      {getSecurityBadge(level)}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{info.description}</p>
                    <ul className="text-xs text-gray-500 space-y-1">
                      {info.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>

              {/* Advanced Options */}
              <div className="pt-4 border-t">
                <h5 className="font-medium mb-3 flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Opções Avançadas
                </h5>
                
                <div className="flex flex-wrap gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={includeWatermark}
                      onChange={(e) => setIncludeWatermark(e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Incluir marca d'água</span>
                  </label>
                  
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={includeSignature}
                      onChange={(e) => setIncludeSignature(e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Incluir assinatura digital</span>
                  </label>
                </div>
              </div>

              {/* Download Button */}
              <Button
                onClick={handleDownload}
                disabled={isDownloading || !downloadStatus?.canDownload}
                className="w-full"
                size="lg"
              >
                {isDownloading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Gerando ebook protegido...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Baixar Ebook Protegido
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Download History */}
      {downloadHistory?.purchases && downloadHistory.purchases.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Histórico de Downloads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {downloadHistory.purchases.map((purchase) => (
                <div key={purchase.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="font-medium">{purchase.productTitle}</p>
                      <p className="text-sm text-gray-600">
                        Comprado em {new Date(purchase.purchaseDate).toLocaleDateString('pt-BR')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {purchase.downloadCount}/{purchase.maxDownloads} downloads
                    </p>
                    {purchase.lastDownload && (
                      <p className="text-xs text-gray-500">
                        Último: {new Date(purchase.lastDownload).toLocaleString('pt-BR')}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Security Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Proteção Anti-Pirataria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-start gap-3">
                <Eye className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-medium">Marca d'Água Personalizada</h5>
                  <p className="text-sm text-gray-600">
                    Seu nome e email são adicionados ao PDF para identificação do comprador.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h5 className="font-medium">Assinatura Digital</h5>
                  <p className="text-sm text-gray-600">
                    Cada download é assinado digitalmente para verificação de autenticidade.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Lock className="h-5 w-5 text-purple-600 mt-0.5" />
                <div>
                  <h5 className="font-medium">Proteção de Conteúdo</h5>
                  <p className="text-sm text-gray-600">
                    Restrições contra cópia, impressão e modificação não autorizadas.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h5 className="font-medium">Rastreamento</h5>
                  <p className="text-sm text-gray-600">
                    Todos os downloads são registrados para combater o compartilhamento ilegal.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <p className="text-sm text-gray-600">
                🔒 <strong>Importante:</strong> Este ebook é protegido por direitos autorais e está licenciado 
                exclusivamente para seu uso pessoal. O compartilhamento, revenda ou distribuição não autorizada 
                é estritamente proibido e pode resultar em ação legal.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}