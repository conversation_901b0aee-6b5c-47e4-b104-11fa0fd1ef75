'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Download, Award, Calendar, FileText, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useToast } from '@ui/hooks/use-toast';
import { apiClient } from '@shared/lib/api-client';

interface CertificateCardProps {
  courseId: string;
  courseName: string;
  productName: string;
  certificate?: {
    id: string;
    certificateCode: string;
    validationCode: string;
    status: 'ACTIVE' | 'REVOKED';
    issuedAt: Date;
    pdfUrl?: string;
  };
  canGenerate?: boolean;
  onGenerate?: () => void;
}

export function CertificateCard({
  courseId,
  courseName,
  productName,
  certificate,
  canGenerate = false,
  onGenerate
}: CertificateCardProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();

  const handleGenerate = async () => {
    if (!canGenerate) return;

    setIsGenerating(true);
    try {
      await apiClient.certificates.generate.mutate({ courseId });
      toast({
        title: 'Certificado gerado!',
        description: 'Seu certificado foi gerado com sucesso.'
      });
      onGenerate?.();
    } catch (error) {
      toast({
        title: 'Erro ao gerar certificado',
        description: 'Não foi possível gerar o certificado. Tente novamente.',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Mutação para download
  const downloadMutation = apiClient.certificates.download.useMutation({
    onSuccess: (result) => {
      if (result.downloadUrl) {
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = result.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: 'Download iniciado',
          description: 'O certificado está sendo baixado.'
        });
      }
    },
    onError: (error) => {
      toast({
        title: 'Erro no download',
        description: 'Não foi possível baixar o certificado.',
        variant: 'destructive'
      });
    }
  });

  const handleDownload = async () => {
    if (!certificate) return;

    setIsDownloading(true);
    try {
      await downloadMutation.mutateAsync({
        certificateId: certificate.id
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge variant="default" className="bg-green-100 text-green-800">Ativo</Badge>;
      case 'REVOKED':
        return <Badge variant="destructive">Revogado</Badge>;
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  return (
    <Card className="border-l-4 border-l-primary">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Award className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Certificado de Conclusão</CardTitle>
          </div>
          {certificate && getStatusBadge(certificate.status)}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-medium">{productName}</h4>
          <p className="text-sm text-muted-foreground">{courseName}</p>
        </div>

        {certificate ? (
          <div className="space-y-3">
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>
                  Emitido em {format(new Date(certificate.issuedAt), 'dd/MM/yyyy', { locale: ptBR })}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="font-mono text-xs">
                  {certificate.certificateCode}
                </span>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleDownload}
                disabled={isDownloading || certificate.status === 'REVOKED'}
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-2" />
                {isDownloading ? 'Baixando...' : 'Baixar PDF'}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const validationUrl = `${window.location.origin}/validate-certificate?code=${certificate.validationCode}`;
                  window.open(validationUrl, '_blank');
                }}
                title="Validar certificado"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : canGenerate ? (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Parabéns! Você concluiu o curso e pode gerar seu certificado.
            </p>
            <Button
              onClick={handleGenerate}
              disabled={isGenerating}
              className="w-full"
            >
              <Award className="h-4 w-4 mr-2" />
              {isGenerating ? 'Gerando...' : 'Gerar Certificado'}
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Complete todas as aulas do curso para gerar seu certificado.
            </p>
            <Button disabled className="w-full">
              <Award className="h-4 w-4 mr-2" />
              Certificado Indisponível
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
