import { Card, CardContent } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { CheckCircle, Clock, Upload, FileText, Camera, AlertCircle } from "lucide-react";
import { apiClient } from "@shared/lib/api-client";
import { useState } from "react";
import { IdentityVerificationDialog } from "./IdentityVerificationDialog";

export function IdentityVerificationContent() {
  const { data: financialData } = apiClient.financial.getBalance.useQuery();
  const [isUploading, setIsUploading] = useState(false);
  const [verificationDialogOpen, setVerificationDialogOpen] = useState(false);
  
  const handleStartVerification = () => {
    setVerificationDialogOpen(true);
  };

  const handleVerificationSuccess = () => {
    // Recarregar a página para atualizar o status
    window.location.reload();
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Coluna Esquerda - Instruções */}
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Completar cadastro</h2>
          <p className="text-gray-600">
            Preencha seus dados para receber o dinheiro das suas vendas
          </p>
        </div>

        {!financialData?.identityVerified && (
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-orange-900 mb-2">Verificação de Identidade Necessária</h3>
                  <p className="text-sm text-orange-700 mb-4">
                    Para poder sacar seus ganhos, você precisa verificar sua identidade. 
                    Este processo é obrigatório por lei e garante a segurança de todos.
                  </p>
                  <Button 
                    onClick={handleStartVerification}
                    className="bg-orange-600 hover:bg-orange-700"
                    disabled={isUploading}
                  >
                    <Camera className="mr-2 h-4 w-4" />
                    {isUploading ? "Processando..." : "Iniciar Verificação"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Como funciona a verificação:</h3>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-bold text-blue-600">1</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Documento de Identidade</p>
                <p className="text-sm text-gray-600">Envie uma foto da frente do seu RG, CNH ou Passaporte</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-bold text-blue-600">2</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Selfie com Documento</p>
                <p className="text-sm text-gray-600">Tire uma foto sua segurando o mesmo documento</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-bold text-blue-600">3</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Aprovação Automática</p>
                <p className="text-sm text-gray-600">Nossa IA analisa e aprova em até 5 minutos</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coluna Direita - Status da Identidade */}
      <div>
        <Card className="bg-white border border-gray-200 shadow-sm rounded-2xl">
          <CardContent className="p-6 text-center">
            {financialData?.identityVerified ? (
              <>
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="h-10 w-10 text-green-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Identidade Verificada</h3>
                <p className="text-gray-600 mb-4">Sua identidade foi verificada com sucesso!</p>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <p className="text-sm text-green-800 font-medium">
                    ✅ Você já pode começar a vender e sacar seus ganhos
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Clock className="h-10 w-10 text-gray-400" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Identidade Pendente</h3>
                <p className="text-gray-600 mb-6">Complete a verificação para começar a vender</p>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <FileText className="h-4 w-4" />
                    <span>Documento de identidade</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Camera className="h-4 w-4" />
                    <span>Selfie com documento</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Upload className="h-4 w-4" />
                    <span>Upload das imagens</span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Modal de Verificação de Identidade */}
      <IdentityVerificationDialog
        open={verificationDialogOpen}
        onOpenChange={setVerificationDialogOpen}
        onSuccess={handleVerificationSuccess}
      />
    </div>
  );
}
