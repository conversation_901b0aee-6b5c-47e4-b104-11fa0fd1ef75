// apps/web/app/[locale]/(platform)/app/finance/components/MoneyInput.tsx

import { useState, useEffect } from "react";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";

interface MoneyInputProps {
  value: number;
  onChange: (value: number) => void;
  max?: number;
  min?: number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function MoneyInput({ 
  value, 
  onChange, 
  max, 
  min = 0, 
  placeholder = "R$ 0,00",
  className,
  disabled = false
}: MoneyInputProps) {
  const [displayValue, setDisplayValue] = useState("");

  // Função para formatar valor como moeda brasileira
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Função para converter string formatada para número
  const parseCurrency = (formattedValue: string) => {
    // Remove tudo que não é número
    const numbers = formattedValue.replace(/\D/g, '');
    return parseInt(numbers) / 100; // Converte centavos para reais
  };

  // Atualizar display quando value prop muda
  useEffect(() => {
    setDisplayValue(formatCurrency(value));
  }, [value]);

  const handleChange = (inputValue: string) => {
    // Se o campo estiver vazio, permitir limpar
    if (inputValue === "") {
      setDisplayValue("");
      onChange(0);
      return;
    }

    // Remove tudo que não é número
    const numbers = inputValue.replace(/\D/g, '');
    
    if (numbers === "") {
      setDisplayValue("");
      onChange(0);
      return;
    }

    // Converte centavos para reais
    const numericValue = parseInt(numbers) / 100;
    
    // Validações
    if (numericValue < min) {
      return; // Não atualiza se for menor que o mínimo
    }
    
    if (max && numericValue > max) {
      return; // Não atualiza se for maior que o máximo
    }

    // Atualiza o valor
    onChange(numericValue);
    setDisplayValue(formatCurrency(numericValue));
  };

  const handleBlur = () => {
    // Garante que o valor seja formatado corretamente ao sair do campo
    setDisplayValue(formatCurrency(value));
  };

  const handleFocus = () => {
    // Remove a formatação ao focar para facilitar edição
    if (value > 0) {
      setDisplayValue(value.toString().replace('.', ','));
    }
  };

  return (
    <Input
      value={displayValue}
      onChange={(e) => handleChange(e.target.value)}
      onBlur={handleBlur}
      onFocus={handleFocus}
      placeholder={placeholder}
      className={cn("text-right font-mono", className)}
      disabled={disabled}
    />
  );
}
