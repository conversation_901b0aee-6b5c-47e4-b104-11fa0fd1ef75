import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@ui/components/dialog";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { useToast } from "@ui/hooks/use-toast";
import { useState } from "react";
import { FileUpload } from "../../products/components/FileUpload";
import { Camera, FileText, Upload, CheckCircle } from "lucide-react";

interface IdentityVerificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function IdentityVerificationDialog({
  open,
  onOpenChange,
  onSuccess
}: IdentityVerificationDialogProps) {
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  const [documents, setDocuments] = useState({
    identity: "",
    selfie: "",
    proofOfAddress: ""
  });

  const handleUpload = async (file: File, type: "identity" | "selfie" | "proofOfAddress") => {
    setUploading(true);
    try {
      // Aqui você pode implementar a lógica de upload
      // Por enquanto, vamos simular o upload
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setDocuments(prev => ({
        ...prev,
        [type]: "uploaded" // URL do documento
      }));
      
      toast({
        title: "Documento enviado com sucesso",
        description: "O documento foi carregado e está sendo processado.",
        variant: "success"
      });
    } catch (error) {
      toast({
        title: "Erro ao enviar documento",
        description: "Tente novamente em alguns instantes.",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
    }
  };

  const handleSubmit = async () => {
    if (!documents.identity || !documents.selfie || !documents.proofOfAddress) {
      toast({
        title: "Documentos incompletos",
        description: "Por favor, envie todos os documentos necessários.",
        variant: "destructive"
      });
      return;
    }

    setUploading(true);
    try {
      // Aqui você pode implementar a lógica de envio para análise
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Verificação enviada com sucesso",
        description: "Sua identidade será verificada em até 5 minutos.",
        variant: "success"
      });
      
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Erro ao enviar verificação",
        description: "Tente novamente em alguns instantes.",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
    }
  };

  const allDocumentsUploaded = documents.identity && documents.selfie && documents.proofOfAddress;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Verificação de Identidade
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Instruções */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">Como funciona a verificação:</h3>
            <div className="space-y-2 text-sm text-blue-800">
              <p>• Envie uma foto da frente do seu RG, CNH ou Passaporte</p>
              <p>• Tire uma selfie segurando o mesmo documento</p>
              <p>• Envie um comprovante de residência (conta de luz, água, etc.)</p>
              <p>• Nossa IA analisa e aprova em até 5 minutos</p>
            </div>
          </div>

          {/* Documento de Identidade */}
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <h3 className="font-medium">Documento de Identidade</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Envie uma foto da frente do seu RG, CNH ou Passaporte
                </p>
                <FileUpload
                  value={documents.identity}
                  onChange={(file) => handleUpload(file, "identity")}
                  accept={{
                    'image/*': ['.jpeg', '.jpg', '.png'],
                    'application/pdf': ['.pdf']
                  }}
                  maxSize={10 * 1024 * 1024 * 1024} // 10GB
                />
                {documents.identity && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    <span>Documento enviado com sucesso</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Selfie com Documento */}
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Camera className="h-4 w-4 text-blue-600" />
                  <h3 className="font-medium">Selfie com Documento</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Tire uma foto sua segurando o mesmo documento
                </p>
                <FileUpload
                  value={documents.selfie}
                  onChange={(file) => handleUpload(file, "selfie")}
                  accept={{
                    'image/*': ['.jpeg', '.jpg', '.png']
                  }}
                  maxSize={10 * 1024 * 1024 * 1024} // 10GB
                />
                {documents.selfie && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    <span>Selfie enviada com sucesso</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Comprovante de Residência */}
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Upload className="h-4 w-4 text-blue-600" />
                  <h3 className="font-medium">Comprovante de Residência</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Envie uma conta de luz, água, telefone ou similar
                </p>
                <FileUpload
                  value={documents.proofOfAddress}
                  onChange={(file) => handleUpload(file, "proofOfAddress")}
                  accept={{
                    'image/*': ['.jpeg', '.jpg', '.png'],
                    'application/pdf': ['.pdf']
                  }}
                  maxSize={10 * 1024 * 1024 * 1024} // 10GB
                />
                {documents.proofOfAddress && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    <span>Comprovante enviado com sucesso</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Botão de Envio */}
          <Button
            className="w-full"
            onClick={handleSubmit}
            loading={uploading}
            disabled={!allDocumentsUploaded || uploading}
            size="lg"
          >
            <Camera className="mr-2 h-4 w-4" />
            {uploading ? "Enviando..." : "Enviar para Análise"}
          </Button>

          {/* Aviso sobre privacidade */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <p className="text-xs text-gray-600">
              <strong>Privacidade:</strong> Seus documentos são criptografados e armazenados com segurança. 
              Eles são usados apenas para verificação de identidade e não são compartilhados com terceiros.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
