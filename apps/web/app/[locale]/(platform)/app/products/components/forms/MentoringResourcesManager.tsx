'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@ui/components/form';
import { Input } from '@ui/components/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Textarea } from '@ui/components/textarea';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@ui/components/dialog';
import { FileText, Plus, Trash2, Edit, ExternalLink, File, Video, Headphones } from 'lucide-react';
import { useToast } from '@ui/hooks/use-toast';
import { apiClient } from '@shared/lib/api-client';

const resourceSchema = z.object({
	title: z.string().min(1, '<PERSON><PERSON>tulo é obrigatório'),
	description: z.string().optional(),
	type: z.string().min(1, 'Tipo é obrigatório'),
	fileUrl: z.string().optional(),
	externalUrl: z.string().optional(),
});

type ResourceFormValues = z.infer<typeof resourceSchema>;

interface MentoringResourcesManagerProps {
	productId: string;
}

export function MentoringResourcesManager({ productId }: MentoringResourcesManagerProps) {
	const { toast } = useToast();
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [editingResource, setEditingResource] = useState<any>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Buscar mentoria para obter o ID
	const { data: mentoring } = apiClient.products.getMentoring.useQuery(
		{ productId },
		{ enabled: !!productId }
	);

	// Buscar recursos existentes
	const { data: resources, refetch: refetchResources } = apiClient.products.getMentoringResources.useQuery(
		{ mentoringId: mentoring?.id || '' },
		{ enabled: !!mentoring?.id }
	);

	// Mutations
	const createMutation = apiClient.products.createMentoringResource.useMutation();
	const updateMutation = apiClient.products.updateMentoringResource.useMutation();
	const deleteMutation = apiClient.products.deleteMentoringResource.useMutation();

	const form = useForm<ResourceFormValues>({
		resolver: zodResolver(resourceSchema),
		defaultValues: {
			title: '',
			description: '',
			type: 'DOCUMENT',
			fileUrl: '',
			externalUrl: '',
		},
	});

	useEffect(() => {
		if (editingResource) {
			form.reset(editingResource);
		} else {
			form.reset({
				title: '',
				description: '',
				type: 'DOCUMENT',
				fileUrl: '',
				externalUrl: '',
			});
		}
	}, [editingResource, form]);

	const onSubmit = async (data: ResourceFormValues) => {
		if (!mentoring?.id) {
			toast({
				title: 'Erro',
				description: 'Mentoria não encontrada',
				variant: 'error',
			});
			return;
		}

		setIsSubmitting(true);
		try {
			if (editingResource) {
				await updateMutation.mutateAsync({
					id: editingResource.id,
					data,
				});
				toast({
					title: 'Recurso atualizado',
					description: 'O recurso foi atualizado com sucesso',
				});
			} else {
				await createMutation.mutateAsync({
					mentoringId: mentoring.id,
					data,
				});
				toast({
					title: 'Recurso criado',
					description: 'O recurso foi criado com sucesso',
				});
			}

			setIsDialogOpen(false);
			setEditingResource(null);
			form.reset();
			refetchResources();
		} catch (error) {
			toast({
				title: 'Erro',
				description: (error as Error).message || 'Erro ao salvar recurso',
				variant: 'error',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleDelete = async (resourceId: string) => {
		if (!confirm('Tem certeza que deseja excluir este recurso?')) return;

		try {
			await deleteMutation.mutateAsync({ id: resourceId });
			toast({
				title: 'Recurso excluído',
				description: 'O recurso foi excluído com sucesso',
			});
			refetchResources();
		} catch (error) {
			toast({
				title: 'Erro',
				description: (error as Error).message || 'Erro ao excluir recurso',
				variant: 'error',
			});
		}
	};

	const handleEdit = (resource: any) => {
		setEditingResource(resource);
		setIsDialogOpen(true);
	};

	const handleCreate = () => {
		setEditingResource(null);
		setIsDialogOpen(true);
	};

	const getTypeIcon = (type: string) => {
		switch (type) {
			case 'VIDEO':
				return <Video className="h-4 w-4" />;
			case 'AUDIO':
				return <Headphones className="h-4 w-4" />;
			case 'LINK':
				return <ExternalLink className="h-4 w-4" />;
			default:
				return <File className="h-4 w-4" />;
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'VIDEO':
				return 'Vídeo';
			case 'AUDIO':
				return 'Áudio';
			case 'LINK':
				return 'Link';
			case 'DOCUMENT':
				return 'Documento';
			default:
				return type;
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h3 className="text-lg font-medium">Recursos da Mentoria</h3>
					<p className="text-sm text-muted-foreground">
						Adicione materiais, links e arquivos para seus alunos
					</p>
				</div>
				<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
					<DialogTrigger asChild>
						<Button onClick={handleCreate}>
							<Plus className="mr-2 h-4 w-4" />
							Adicionar Recurso
						</Button>
					</DialogTrigger>
					<DialogContent className="sm:max-w-[500px]">
						<DialogHeader>
							<DialogTitle>
								{editingResource ? 'Editar Recurso' : 'Adicionar Recurso'}
							</DialogTitle>
						</DialogHeader>
						<Form {...form}>
							<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
								<FormField
									control={form.control}
									name="title"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Título</FormLabel>
											<FormControl>
												<Input placeholder="Nome do recurso" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Descrição</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Descrição opcional do recurso"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="type"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Tipo</FormLabel>
											<Select onValueChange={field.onChange} defaultValue={field.value}>
												<FormControl>
													<SelectTrigger>
														<SelectValue />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="DOCUMENT">Documento</SelectItem>
													<SelectItem value="VIDEO">Vídeo</SelectItem>
													<SelectItem value="AUDIO">Áudio</SelectItem>
													<SelectItem value="LINK">Link Externo</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{form.watch('type') === 'LINK' ? (
									<FormField
										control={form.control}
										name="externalUrl"
										render={({ field }) => (
											<FormItem>
												<FormLabel>URL Externa</FormLabel>
												<FormControl>
													<Input
														placeholder="https://..."
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								) : (
									<FormField
										control={form.control}
										name="fileUrl"
										render={({ field }) => (
											<FormItem>
												<FormLabel>URL do Arquivo</FormLabel>
												<FormControl>
													<Input
														placeholder="https://..."
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								<div className="flex justify-end gap-3">
									<Button
										type="button"
										variant="outline"
										onClick={() => setIsDialogOpen(false)}
									>
										Cancelar
									</Button>
									<Button type="submit" disabled={isSubmitting}>
										{isSubmitting ? 'Salvando...' : editingResource ? 'Atualizar' : 'Criar'}
									</Button>
								</div>
							</form>
						</Form>
					</DialogContent>
				</Dialog>
			</div>

			{/* Lista de recursos */}
			<div className="space-y-4">
				{resources && resources.length > 0 ? (
					resources.map((resource) => (
						<Card key={resource.id} className="p-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
										{getTypeIcon(resource.type)}
									</div>
									<div>
										<div className="flex items-center gap-2">
											<h4 className="font-medium">{resource.title}</h4>
											<Badge variant="outline">
												{getTypeLabel(resource.type)}
											</Badge>
										</div>
										{resource.description && (
											<p className="text-sm text-muted-foreground">
												{resource.description}
											</p>
										)}
										{resource.externalUrl && (
											<a
												href={resource.externalUrl}
												target="_blank"
												rel="noopener noreferrer"
												className="text-sm text-primary hover:underline"
											>
												{resource.externalUrl}
											</a>
										)}
									</div>
								</div>
								<div className="flex items-center gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => handleEdit(resource)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="outline"
										onClick={() => handleDelete(resource.id)}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</Card>
					))
				) : (
					<div className="text-center py-8 text-muted-foreground">
						<FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Nenhum recurso criado ainda</p>
						<p className="text-sm">Clique em "Adicionar Recurso" para começar</p>
					</div>
				)}
			</div>
		</div>
	);
}
