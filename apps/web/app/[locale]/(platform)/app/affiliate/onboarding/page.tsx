"use client";

import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { apiClient } from "@shared/lib/api-client";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@ui/hooks/use-toast";

const affiliateFormSchema = z.object({
  pixKey: z.string().optional(),
  bank: z.string().min(1, "Banco é obrigatório"),
  agency: z.string().min(1, "Agência é obrigatória"),
  account: z.string().min(1, "Conta é obrigatória"),
  accountType: z.enum(["checking", "savings"], {
    required_error: "Selecione o tipo de conta",
  }),
});

type FormValues = z.infer<typeof affiliateFormSchema>;

export default function AffiliateOnboardingPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();
  const productId = searchParams.get("productId") ?? "";

  const form = useForm<FormValues>({
    resolver: zodResolver(affiliateFormSchema),
    defaultValues: {
      pixKey: "",
      bank: "",
      agency: "",
      account: "",
      accountType: "checking",
    },
  });

  const becomeAffiliateMutation = apiClient.affiliates.becomeAffiliate.useMutation();
  const generateLinkMutation = apiClient.products.generateAffiliateLink.useMutation();

  const onSubmit = async (values: FormValues) => {
    try {
      await becomeAffiliateMutation.mutateAsync({
        pixKey: values.pixKey,
        bankAccount: {
          bank: values.bank,
          agency: values.agency,
          account: values.account,
          type: values.accountType,
        },
      });

      if (productId) {
        await generateLinkMutation.mutateAsync({ productId });
      }

      toast({
        title: "Perfil de afiliado criado",
        description: productId
          ? "Seu link para o produto foi gerado com sucesso"
          : "Você já pode promover produtos como afiliado",
        variant: "success",
      });

      router.push(productId ? "/app/my-affiliate-products" : "/app/affiliates");
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error?.message || "Não foi possível concluir o onboarding",
        variant: "error",
      });
    }
  };

  return (
    <div className="container py-8">
      <PageHeader
        title="Onboarding de Afiliado"
        subtitle={productId ? "Conclua seus dados para gerar seu link deste produto" : "Conclua seus dados para começar a promover produtos"}
      />

      <Card className="max-w-2xl mx-auto mt-8">
        <CardHeader>
          <CardTitle>Informações de Pagamento</CardTitle>
          <CardDescription>Utilizadas para pagamento de comissões</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="pixKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Chave PIX (opcional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Sua chave PIX" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="bank"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Banco</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: 001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="agency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Agência</FormLabel>
                      <FormControl>
                        <Input placeholder="Sem dígito" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="account"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Conta</FormLabel>
                      <FormControl>
                        <Input placeholder="Com dígito" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="accountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Conta</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="checking">Corrente</SelectItem>
                          <SelectItem value="savings">Poupança</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end">
                <Button type="submit">Concluir Onboarding</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}


