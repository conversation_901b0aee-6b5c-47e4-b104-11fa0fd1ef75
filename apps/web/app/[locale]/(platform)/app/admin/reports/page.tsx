'use client';

import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import { Skeleton } from '@ui/components/skeleton';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatCurrency } from 'utils';
import { 
	TrendingUp,
	Users,
	ShoppingCart,
	BookOpen
} from 'lucide-react';
import {
	Bar,
	BarChart,
	ResponsiveContainer,
	XAxis,
	YAxis,
	Tooltip,
	CartesianGrid,
	Pie,
	PieChart,
	Cell,
} from 'recharts';

export default function AdminReportsPage() {
	const [dateRange, setDateRange] = useState('30');

	// Buscar dados para relatórios
	const { data: dashboardStats } = apiClient.admin.getDashboardStats.useQuery();
	const { data: products } = apiClient.admin.listAllProducts.useQuery({
		page: 1,
		pageSize: 100,
	});
	const { data: orders } = apiClient.admin.listAllOrders.useQuery({
		page: 1,
		pageSize: 100,
	});

	// Preparar dados para gráficos
	const salesByProduct = products?.products.map(product => ({
		name: product.title.length > 20 ? product.title.substring(0, 20) + '...' : product.title,
		vendas: product._count.orders,
		receita: Number(product.totalSales),
		type: product.type,
	})) || [];

	const salesByType = [
		{
			name: 'Cursos',
			value: products?.products.filter(p => p.type === 'COURSE').length || 0,
			color: '#8884d8'
		},
		{
			name: 'Ebooks', 
			value: products?.products.filter(p => p.type === 'EBOOK').length || 0,
			color: '#82ca9d'
		},
		{
			name: 'Mentorias',
			value: products?.products.filter(p => p.type === 'MENTORING').length || 0,
			color: '#ffc658'
		}
	];

	const monthlyRevenue = dashboardStats?.growthChart.map(item => ({
		mes: format(new Date(item.date), 'dd/MM', { locale: ptBR }),
		receita: item.revenue,
		pedidos: item.orders,
	})) || [];


	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Relatórios</h1>
					<p className="text-muted-foreground">
						Análises e insights da plataforma
					</p>
				</div>
				
				{/* Filtro de Período Simplificado */}
				<div className="flex items-center gap-2">
					<label className="text-sm font-medium whitespace-nowrap">Período:</label>
					<Select value={dateRange} onValueChange={setDateRange}>
						<SelectTrigger className="w-48 min-w-48">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="7">Últimos 7 dias</SelectItem>
							<SelectItem value="30">Últimos 30 dias</SelectItem>
							<SelectItem value="90">Últimos 90 dias</SelectItem>
							<SelectItem value="365">Último ano</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Métricas Principais */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Total</CardTitle>
						<TrendingUp className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{formatCurrency(dashboardStats?.totalRevenue || 0)}
						</div>
						<p className="text-xs text-muted-foreground">
							+{formatCurrency(dashboardStats?.monthlyStats.newRevenue || 0)} este mês
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Pedidos</CardTitle>
						<ShoppingCart className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{dashboardStats?.totalOrders || 0}</div>
						<p className="text-xs text-muted-foreground">
							+{dashboardStats?.monthlyStats.newOrders || 0} este mês
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Usuários</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{dashboardStats?.totalUsers || 0}</div>
						<p className="text-xs text-muted-foreground">
							+{dashboardStats?.monthlyStats.newUsers || 0} este mês
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Produtos</CardTitle>
						<BookOpen className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{dashboardStats?.totalProducts || 0}</div>
						<p className="text-xs text-muted-foreground">
							+{dashboardStats?.monthlyStats.newProducts || 0} este mês
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Gráficos */}
			<div className="grid gap-6 md:grid-cols-2">
				{/* Receita por Mês */}
				<Card>
					<CardHeader>
						<CardTitle>Receita por Período</CardTitle>
						<CardDescription>
							Evolução da receita nos últimos {dateRange} dias
						</CardDescription>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={300}>
							<BarChart data={monthlyRevenue}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="mes" />
								<YAxis />
								<Tooltip 
									formatter={(value: any) => formatCurrency(value)}
									labelFormatter={(label) => `Data: ${label}`}
								/>
								<Bar dataKey="receita" fill="#8884d8" />
							</BarChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>

				{/* Distribuição por Tipo */}
				<Card>
					<CardHeader>
						<CardTitle>Distribuição por Tipo</CardTitle>
						<CardDescription>
							Quantidade de produtos por categoria
						</CardDescription>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={300}>
							<PieChart>
								<Pie
									data={salesByType}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
									outerRadius={80}
									fill="#8884d8"
									dataKey="value"
								>
									{salesByType.map((entry, index) => (
										<Cell key={`cell-${index}`} fill={entry.color} />
									))}
								</Pie>
								<Tooltip />
							</PieChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>
			</div>

			{/* Top Produtos */}
			<Card>
				<CardHeader>
					<CardTitle>Top Produtos por Vendas</CardTitle>
					<CardDescription>
						Ranking dos produtos mais vendidos
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{salesByProduct
							.sort((a, b) => b.vendas - a.vendas)
							.slice(0, 10)
							.map((product, index) => (
								<div key={index} className="flex items-center justify-between p-3 border rounded-lg">
									<div className="flex items-center gap-3">
										<div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-sm font-bold">
											{index + 1}
										</div>
										<div>
											<p className="font-medium">{product.name}</p>
											<p className="text-sm text-muted-foreground">
												{product.type} • {product.vendas} vendas
											</p>
										</div>
									</div>
									<div className="text-right">
										<p className="font-semibold">
											{formatCurrency(product.receita)}
										</p>
									</div>
								</div>
							))}
					</div>
				</CardContent>
			</Card>

		</div>
	);
}
