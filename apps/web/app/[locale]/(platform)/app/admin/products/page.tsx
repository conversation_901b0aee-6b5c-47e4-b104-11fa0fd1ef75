'use client';

import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@ui/components/table';
import { Skeleton } from '@ui/components/skeleton';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatCurrency } from 'utils';
import { SearchIcon, Eye, ExternalLink } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function AdminProductsPage() {
	const [search, setSearch] = useState('');
	const [type, setType] = useState<string>('');
	const [status, setStatus] = useState<string>('');
	const [page, setPage] = useState(1);

	const { data, isLoading } = apiClient.admin.listAllProducts.useQuery({
		search: search || undefined,
		type: type && type !== 'all' ? (type as any) : undefined,
		status: status && status !== 'all' ? (status as any) : undefined,
		page,
		pageSize: 20,
	});

	const productTypeLabels = {
		COURSE: 'Curso',
		EBOOK: 'Ebook',
		MENTORING: 'Mentoria',
	};

	const productStatusLabels = {
		DRAFT: 'Rascunho',
		PUBLISHED: 'Publicado',
		ARCHIVED: 'Arquivado',
	};

	const productStatusColors = {
		DRAFT: 'bg-yellow-500',
		PUBLISHED: 'bg-green-500',
		ARCHIVED: 'bg-gray-500',
	};

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Gerenciar Produtos</h1>
				<p className="text-muted-foreground">
					Visualize e gerencie todos os produtos da plataforma
				</p>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Filtros</CardTitle>
					<CardDescription>
						Busque e filtre produtos por critérios específicos
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-3">
						<div className="relative">
							<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
							<Input
								placeholder="Buscar por título, descrição..."
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								className="pl-10"
							/>
						</div>

						<Select value={type} onValueChange={setType}>
							<SelectTrigger>
								<SelectValue placeholder="Tipo de produto" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todos os tipos</SelectItem>
								<SelectItem value="COURSE">Cursos</SelectItem>
								<SelectItem value="EBOOK">Ebooks</SelectItem>
								<SelectItem value="MENTORING">Mentorias</SelectItem>
							</SelectContent>
						</Select>

						<Select value={status} onValueChange={setStatus}>
							<SelectTrigger>
								<SelectValue placeholder="Status" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Status</SelectItem>
								<SelectItem value="DRAFT">Rascunho</SelectItem>
								<SelectItem value="PUBLISHED">Publicado</SelectItem>
								<SelectItem value="ARCHIVED">Arquivado</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Produtos</CardTitle>
					<CardDescription>
						{data?.pagination.total || 0} produtos encontrados
					</CardDescription>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-3">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-20" />
							))}
						</div>
					) : (
						<>
							<div className="overflow-x-auto">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Produto</TableHead>
											<TableHead>Tipo</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Criador</TableHead>
											<TableHead>Preço</TableHead>
											<TableHead>Vendas</TableHead>
											<TableHead>Criado em</TableHead>
											<TableHead className="text-right">Ações</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{data?.products.map((product) => (
											<TableRow key={product.id}>
												<TableCell>
													<div className="flex items-center gap-3">
														{product.thumbnail && (
															<div className="relative h-12 w-12 overflow-hidden rounded">
																<Image
																	src={product.thumbnail}
																	alt={product.title}
																	fill
																	className="object-cover"
																/>
															</div>
														)}
														<div>
															<p className="font-medium">{product.title}</p>
															<p className="text-xs text-muted-foreground">
																{product.slug}
															</p>
														</div>
													</div>
												</TableCell>
												<TableCell>
													<Badge variant="outline">
														{productTypeLabels[product.type]}
													</Badge>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<div
															className={`h-2 w-2 rounded-full ${
																productStatusColors[product.status]
															}`}
														/>
														{productStatusLabels[product.status]}
													</div>
												</TableCell>
												<TableCell>
													<div>
														<p className="text-sm">
															{product.creator.name || product.creator.email}
														</p>
														<p className="text-xs text-muted-foreground">
															{product.creator.email}
														</p>
													</div>
												</TableCell>
												<TableCell>
													{formatCurrency(Number(product.price))}
												</TableCell>
												<TableCell>
													<div>
														<p className="font-medium">
															{product._count.orders}
														</p>
														<p className="text-xs text-muted-foreground">
															{product.type === 'COURSE' &&
																product.course &&
																`${product.course._count.enrollments} matrículas`}
														</p>
													</div>
												</TableCell>
												<TableCell>
													{format(new Date(product.createdAt), 'dd/MM/yyyy', {
														locale: ptBR,
													})}
												</TableCell>
												<TableCell className="text-right">
													<div className="flex justify-end gap-2">
														<Button variant="outline" size="sm" asChild>
															<Link
																href={`/app/products/${product.id}`}
																target="_blank"
															>
																<Eye className="mr-2 h-4 w-4" />
																Ver
															</Link>
														</Button>
														<Button variant="outline" size="sm" asChild>
															<Link
																href={`/p/${product.slug}`}
																target="_blank"
															>
																<ExternalLink className="h-4 w-4" />
															</Link>
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{data && data.pagination.totalPages > 1 && (
								<div className="mt-4 flex items-center justify-between">
									<p className="text-sm text-muted-foreground">
										Página {data.pagination.page} de{' '}
										{data.pagination.totalPages}
									</p>
									<div className="flex gap-2">
										<Button
											variant="outline"
											size="sm"
											disabled={page === 1}
											onClick={() => setPage(page - 1)}
										>
											Anterior
										</Button>
										<Button
											variant="outline"
											size="sm"
											disabled={page === data.pagination.totalPages}
											onClick={() => setPage(page + 1)}
										>
											Próxima
										</Button>
									</div>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

