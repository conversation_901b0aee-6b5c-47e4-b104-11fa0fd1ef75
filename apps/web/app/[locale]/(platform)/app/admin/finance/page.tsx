'use client';

import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@ui/components/table';
import { Skeleton } from '@ui/components/skeleton';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatCurrency } from 'utils';
import { SearchIcon, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';

export default function AdminFinancePage() {
	const [search, setSearch] = useState('');
	const [status, setStatus] = useState<string>('');
	const [page, setPage] = useState(1);

	// Buscar dados financeiros (usando dados existentes)
	const { data: orders } = apiClient.admin.listAllOrders.useQuery({
		search: search || undefined,
		status: status && status !== 'all' ? (status as any) : undefined,
		page,
		pageSize: 20,
	});

	// Calcular métricas financeiras
	const totalRevenue = orders?.orders.reduce((acc, order) => {
		return acc + Number(order.amount);
	}, 0) || 0;

	const paidOrders = orders?.orders.filter(order => order.status === 'PAID') || [];
	const pendingOrders = orders?.orders.filter(order => order.status === 'PENDING') || [];
	const failedOrders = orders?.orders.filter(order => order.status === 'FAILED') || [];

	const paidRevenue = paidOrders.reduce((acc, order) => acc + Number(order.amount), 0);
	const pendingRevenue = pendingOrders.reduce((acc, order) => acc + Number(order.amount), 0);

	const orderStatusLabels = {
		PENDING: 'Pendente',
		PROCESSING: 'Processando',
		PAID: 'Pago',
		FAILED: 'Falhou',
		REFUNDED: 'Reembolsado',
		CANCELLED: 'Cancelado',
	};

	const orderStatusColors = {
		PENDING: 'bg-yellow-500',
		PROCESSING: 'bg-blue-500',
		PAID: 'bg-green-500',
		FAILED: 'bg-red-500',
		REFUNDED: 'bg-gray-500',
		CANCELLED: 'bg-gray-400',
	};

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Financeiro</h1>
				<p className="text-muted-foreground">
					Visão geral financeira e controle de receitas
				</p>
			</div>

			{/* Métricas Financeiras */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Total</CardTitle>
						<DollarSign className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
						<p className="text-xs text-muted-foreground">
							{orders?.orders.length || 0} transações
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Confirmada</CardTitle>
						<TrendingUp className="h-4 w-4 text-green-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{formatCurrency(paidRevenue)}
						</div>
						<p className="text-xs text-muted-foreground">
							{paidOrders.length} pedidos pagos
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pendente</CardTitle>
						<TrendingDown className="h-4 w-4 text-yellow-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-yellow-600">
							{formatCurrency(pendingRevenue)}
						</div>
						<p className="text-xs text-muted-foreground">
							{pendingOrders.length} pedidos pendentes
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Taxa de Conversão</CardTitle>
						<TrendingUp className="h-4 w-4 text-blue-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-blue-600">
							{orders?.orders.length ? 
								Math.round((paidOrders.length / orders.orders.length) * 100) : 0}%
						</div>
						<p className="text-xs text-muted-foreground">
							Pedidos convertidos
						</p>
					</CardContent>
				</Card>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Filtros</CardTitle>
					<CardDescription>
						Busque e filtre transações financeiras
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-2">
						<div className="relative">
							<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
							<Input
								placeholder="Buscar por email, nome, CPF..."
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								className="pl-10"
							/>
						</div>

						<Select value={status} onValueChange={setStatus}>
							<SelectTrigger>
								<SelectValue placeholder="Status do pagamento" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Status</SelectItem>
								<SelectItem value="PENDING">Pendente</SelectItem>
								<SelectItem value="PROCESSING">Processando</SelectItem>
								<SelectItem value="PAID">Pago</SelectItem>
								<SelectItem value="FAILED">Falhou</SelectItem>
								<SelectItem value="REFUNDED">Reembolsado</SelectItem>
								<SelectItem value="CANCELLED">Cancelado</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Transações Financeiras</CardTitle>
					<CardDescription>
						{orders?.pagination.total || 0} transações encontradas
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Cliente</TableHead>
									<TableHead>Produto</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Valor</TableHead>
									<TableHead>Data</TableHead>
									<TableHead>Gateway</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{orders?.orders.map((order) => (
									<TableRow key={order.id}>
										<TableCell>
											<div>
												<p className="font-medium">
													{order.user.name || order.user.email}
												</p>
												<p className="text-xs text-muted-foreground">
													{order.user.email}
												</p>
											</div>
										</TableCell>
										<TableCell>
											<p className="font-medium">{order.product.title}</p>
											<p className="text-xs text-muted-foreground">
												{order.product.creator.name || order.product.creator.email}
											</p>
										</TableCell>
										<TableCell>
											<div className="flex items-center gap-2">
												<div
													className={`h-2 w-2 rounded-full ${
														orderStatusColors[order.status]
													}`}
												/>
												{orderStatusLabels[order.status]}
											</div>
										</TableCell>
										<TableCell>
											<div>
												<p className="font-medium">
													{formatCurrency(Number(order.amount))}
												</p>
												{order.installments > 1 && (
													<p className="text-xs text-muted-foreground">
														{order.installments}x
													</p>
												)}
											</div>
										</TableCell>
										<TableCell>
											<div>
												<p className="text-sm">
													{format(new Date(order.createdAt), 'dd/MM/yyyy', {
														locale: ptBR,
													})}
												</p>
												<p className="text-xs text-muted-foreground">
													{format(new Date(order.createdAt), 'HH:mm', {
														locale: ptBR,
													})}
												</p>
											</div>
										</TableCell>
										<TableCell>
											{order.gatewayId ? (
												<Badge className="text-xs bg-green-100 text-green-800">
													Processado
												</Badge>
											) : (
												<Badge className="text-xs bg-gray-100 text-gray-800">
													Pendente
												</Badge>
											)}
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</div>

					{orders && orders.pagination.totalPages > 1 && (
						<div className="mt-4 flex items-center justify-between">
							<p className="text-sm text-muted-foreground">
								Página {orders.pagination.page} de {orders.pagination.totalPages}
							</p>
							<div className="flex gap-2">
								<Button
									variant="outline"
									size="sm"
									disabled={page === 1}
									onClick={() => setPage(page - 1)}
								>
									Anterior
								</Button>
								<Button
									variant="outline"
									size="sm"
									disabled={page === orders.pagination.totalPages}
									onClick={() => setPage(page + 1)}
								>
									Próxima
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
