'use client';

import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@ui/components/table';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import { DatePickerWithRange } from '@ui/components/date-picker';
import { addDays } from 'date-fns';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatCurrency } from 'utils';
import { Skeleton } from '@ui/components/skeleton';
import { Pagination } from '@saas/shared/components/Pagination';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@ui/components/dialog';
import { Textarea } from '@ui/components/textarea';
import { Label } from '@ui/components/label';
import { useToast } from '@ui/hooks/use-toast';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@ui/components/alert-dialog';
import {
	CheckCircle,
	XCircle,
	Clock,
	AlertCircle,
	Eye,
	User,
	Calendar,
	DollarSign,
} from 'lucide-react';

export default function AdminWithdrawalsPage() {
	const [page, setPage] = useState(1);
	const [status, setStatus] = useState('ALL');
	const [dateRange, setDateRange] = useState({
		from: addDays(new Date(), -30),
		to: new Date(),
	});
	const [selectedWithdrawal, setSelectedWithdrawal] = useState<any>(null);
	const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
	const [newStatus, setNewStatus] = useState('');
	const [notes, setNotes] = useState('');
	const { toast } = useToast();

	const { data: withdrawals, isLoading, refetch, error } = apiClient.admin.getWithdrawals.useQuery({
		page,
		perPage: 10,
		status: status as any,
		// Temporariamente removendo filtros de data para debug
		// startDate: dateRange.from,
		// endDate: dateRange.to,
	});

	// Debug: verificar se há dados
	if (withdrawals) {
		console.log('✅ Withdrawals carregados:', withdrawals.data?.length || 0, 'saques');
	} else if (error) {
		console.error('❌ Erro ao carregar saques:', error.message);
	}

	const updateStatusMutation = apiClient.admin.updateWithdrawalStatus.useMutation({
		onSuccess: () => {
			toast({
				title: 'Status atualizado com sucesso',
				description: 'O status do saque foi atualizado.',
			});
			setUpdateDialogOpen(false);
			setSelectedWithdrawal(null);
			setNewStatus('');
			setNotes('');
			refetch();
		},
		onError: (error) => {
			toast({
				title: 'Erro ao atualizar status',
				description: error.message,
				variant: 'error',
			});
		},
	});

	const statusColors = {
		PENDING: 'bg-yellow-500',
		PROCESSING: 'bg-blue-500',
		COMPLETED: 'bg-green-600',
		FAILED: 'bg-red-500',
		CANCELLED: 'bg-gray-500',
	};

	const statusLabels = {
		PENDING: 'Pendente',
		PROCESSING: 'Processando',
		COMPLETED: 'Concluído',
		FAILED: 'Falhou',
		CANCELLED: 'Cancelado',
	};

	const statusIcons = {
		PENDING: Clock,
		PROCESSING: AlertCircle,
		COMPLETED: CheckCircle,
		FAILED: XCircle,
		CANCELLED: XCircle,
	};

	const handleUpdateStatus = (withdrawal: any) => {
		setSelectedWithdrawal(withdrawal);
		setNewStatus('');
		setNotes('');
		setUpdateDialogOpen(true);
	};

	const confirmUpdateStatus = async () => {
		if (!selectedWithdrawal || !newStatus) return;

		await updateStatusMutation.mutateAsync({
			withdrawalId: selectedWithdrawal.id,
			status: newStatus as any,
			notes: notes || undefined,
		});
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div>
					<h1 className="text-3xl font-bold">Gerenciar Saques</h1>
					<p className="text-muted-foreground">
						Aprove e gerencie solicitações de saque dos professores
					</p>
				</div>

				<div className="space-y-4">
					{[...Array(5)].map((_, i) => (
						<Skeleton key={i} className="h-20" />
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Gerenciar Saques</h1>
					<p className="text-muted-foreground">
						Aprove e gerencie solicitações de saque dos professores
					</p>
				</div>
				
				{/* Filtros simplificados */}
				<div className="flex items-center gap-4">
					<Select value={status} onValueChange={setStatus}>
						<SelectTrigger className="w-40">
							<SelectValue placeholder="Status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="ALL">Todos</SelectItem>
							<SelectItem value="PENDING">Pendente</SelectItem>
							<SelectItem value="PROCESSING">Processando</SelectItem>
							<SelectItem value="COMPLETED">Concluído</SelectItem>
							<SelectItem value="FAILED">Falhou</SelectItem>
							<SelectItem value="CANCELLED">Cancelado</SelectItem>
						</SelectContent>
					</Select>

				 
				</div>
			</div>

			{/* Lista de Saques */}
			<Card>
				<CardHeader>
					<CardTitle>Solicitações de Saque</CardTitle>
					<CardDescription>
						{withdrawals?.meta.total || 0} saques encontrados
					</CardDescription>
				</CardHeader>
				<CardContent>
					{error && (
						<div className="text-center py-8 text-red-600">
							Erro ao carregar saques: {error.message}
						</div>
					)}
					{withdrawals?.data && withdrawals.data.length > 0 ? (
						<>
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>Professor</TableHead>
										<TableHead>Valor</TableHead>
										<TableHead>Status</TableHead>
										<TableHead>Data</TableHead>
										<TableHead>Ações</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{withdrawals.data.map((withdrawal) => {
										const StatusIcon = statusIcons[withdrawal.status as keyof typeof statusIcons];
										
										return (
											<TableRow key={withdrawal.id}>
												<TableCell>
													<div className="flex items-center gap-2">
														<User className="h-4 w-4 text-muted-foreground" />
														<div>
															<p className="font-medium">
																{withdrawal.user.name || 'Nome não informado'}
															</p>
															<p className="text-sm text-muted-foreground">
																{withdrawal.user.email}
															</p>
														</div>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-1">
														<DollarSign className="h-4 w-4 text-green-600" />
														<span className="font-semibold">
															{formatCurrency(Number(withdrawal.amount))}
														</span>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<StatusIcon className="h-4 w-4" />
														<Badge
															className={`border-0 ${
																statusColors[withdrawal.status as keyof typeof statusColors]
															} text-white font-medium`}
														>
															{statusLabels[withdrawal.status as keyof typeof statusLabels]}
														</Badge>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-1">
														<Calendar className="h-4 w-4 text-muted-foreground" />
														<span className="text-sm">
															{format(new Date(withdrawal.createdAt), 'dd/MM/yyyy HH:mm', {
																locale: ptBR,
															})}
														</span>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() => handleUpdateStatus(withdrawal)}
															disabled={withdrawal.status === 'COMPLETED' || withdrawal.status === 'FAILED' || withdrawal.status === 'CANCELLED'}
														>
															<Eye className="h-4 w-4 mr-1" />
															Gerenciar
														</Button>
													</div>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>

							{withdrawals.meta.total > 10 && (
								<div className="mt-6">
									<Pagination
										currentPage={page}
										totalItems={withdrawals.meta.total}
										itemsPerPage={10}
										onChangeCurrentPage={setPage}
									/>
								</div>
							)}
						</>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							Nenhum saque encontrado para os filtros selecionados
						</div>
					)}
				</CardContent>
			</Card>

			{/* Dialog de Atualização de Status */}
			<Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Atualizar Status do Saque</DialogTitle>
					</DialogHeader>

					{selectedWithdrawal && (
						<div className="space-y-4">
							<div className="space-y-2">
								<Label>Professor</Label>
								<p className="font-medium">
									{selectedWithdrawal.user.name || 'Nome não informado'}
								</p>
								<p className="text-sm text-muted-foreground">
									{selectedWithdrawal.user.email}
								</p>
							</div>

							<div className="space-y-2">
								<Label>Valor</Label>
								<p className="font-semibold text-lg">
									{formatCurrency(selectedWithdrawal.amount)}
								</p>
							</div>

							<div className="space-y-2">
								<Label>Novo Status</Label>
								<Select value={newStatus} onValueChange={setNewStatus}>
									<SelectTrigger>
										<SelectValue placeholder="Selecione o novo status" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="PROCESSING">Processando</SelectItem>
										<SelectItem value="COMPLETED">Concluído</SelectItem>
										<SelectItem value="FAILED">Falhou</SelectItem>
										<SelectItem value="CANCELLED">Cancelado</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-2">
								<Label htmlFor="notes">Observações (opcional)</Label>
								<Textarea
									id="notes"
									placeholder="Adicione observações sobre a mudança de status..."
									value={notes}
									onChange={(e) => setNotes(e.target.value)}
								/>
							</div>

							<div className="flex gap-2 pt-4">
								<Button
									variant="outline"
									onClick={() => setUpdateDialogOpen(false)}
									className="flex-1"
								>
									Cancelar
								</Button>
								<Button
									onClick={confirmUpdateStatus}
									disabled={!newStatus || updateStatusMutation.isPending}
									className="flex-1"
								>
									{updateStatusMutation.isPending ? 'Atualizando...' : 'Atualizar Status'}
								</Button>
							</div>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
