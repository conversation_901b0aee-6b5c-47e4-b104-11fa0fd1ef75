'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from '@ui/components/sheet';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { formatCurrency } from 'utils';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ExternalLink, Eye, User, ShoppingCart, CreditCard, Calendar } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface OrderDetailsSheetProps {
  order: {
    id: string;
    amount: number;
    status: string;
    installments?: number;
    createdAt: string | Date;
    updatedAt?: string;
    user: {
      id: string;
      name?: string | null;
      email: string;
    };
    product: {
      id: string;
      title: string;
      thumbnail?: string | null;
      type: string;
    };
    affiliate?: {
      id: string;
      name?: string;
      email: string;
    };
    coupon?: {
      code: string;
      type: string;
      value: number;
    };
  } & Record<string, any>;
  children: React.ReactNode;
}

export function OrderDetailsSheet({ order, children }: OrderDetailsSheetProps) {
  const [open, setOpen] = useState(false);

  const statusColors = {
    PENDING: 'bg-yellow-500',
    PROCESSING: 'bg-blue-500',
    PAID: 'bg-green-500',
    FAILED: 'bg-red-500',
    REFUNDED: 'bg-gray-500',
    CANCELLED: 'bg-gray-400',
  };

  const statusLabels = {
    PENDING: 'Pendente',
    PROCESSING: 'Processando',
    PAID: 'Pago',
    FAILED: 'Falhou',
    REFUNDED: 'Reembolsado',
    CANCELLED: 'Cancelado',
  };

  const typeLabels = {
    COURSE: 'Curso',
    EBOOK: 'E-book',
    MENTORING: 'Mentoria',
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Detalhes do Pedido
          </SheetTitle>
        </SheetHeader>
        
        <div className="mt-6 space-y-6">
          {/* Status do Pedido */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className={`h-3 w-3 rounded-full ${
                  statusColors[order.status as keyof typeof statusColors]
                }`}
              />
              <span className="font-medium">
                {statusLabels[order.status as keyof typeof statusLabels]}
              </span>
            </div>
            <Badge variant="outline">
              {order.installments && order.installments > 1 ? `${order.installments}x` : 'À vista'}
            </Badge>
          </div>

          {/* Informações do Cliente */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <User className="h-4 w-4" />
                Cliente
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div>
                  <p className="font-medium">{order.user.name || 'Nome não informado'}</p>
                  <p className="text-sm text-muted-foreground">{order.user.email}</p>
                </div>
                {order.affiliate && (
                  <div className="pt-2 border-t">
                    <p className="text-sm font-medium text-muted-foreground">Afiliado</p>
                    <p className="text-sm">{order.affiliate.name || order.affiliate.email}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Produto */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Produto
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-start gap-3">
                {order.product.thumbnail && (
                  <div className="relative h-12 w-12 overflow-hidden rounded">
                    <Image
                      src={order.product.thumbnail}
                      alt={order.product.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <div className="flex-1">
                  <p className="font-medium text-sm">{order.product.title}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {typeLabels[order.product.type as keyof typeof typeLabels] || order.product.type}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resumo Financeiro */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Resumo Financeiro
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Valor do Pedido</span>
                  <span className="font-medium">{formatCurrency(order.amount)}</span>
                </div>
                
                {order.coupon && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Cupom ({order.coupon.code})
                    </span>
                    <span className="text-sm text-green-600">
                      -{formatCurrency(order.coupon.value)}
                    </span>
                  </div>
                )}
                
                {order.installments && order.installments > 1 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Parcelas</span>
                    <span className="text-sm">{order.installments}x de {formatCurrency(order.amount / order.installments)}</span>
                  </div>
                )}
                
                <Separator />
                
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>{formatCurrency(order.amount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações do Pedido */}
          <div>
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Informações do Pedido
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">ID do Pedido</span>
                <span className="text-sm font-mono">{order.id}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Criado em</span>
                <span className="text-sm">
                  {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                </span>
              </div>
              
              {order.updatedAt && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Atualizado em</span>
                  <span className="text-sm">
                    {format(new Date(order.updatedAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                  </span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Ações */}
          <div className="flex gap-2">
            <Button asChild variant="outline" className="flex-1">
              <Link href={`/app/admin/orders/${order.id}`}>
                <Eye className="h-4 w-4 mr-2" />
                Ver Detalhes
              </Link>
            </Button>
            <Button asChild className="flex-1">
              <Link href={`/app/admin/orders`}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Gerenciar
              </Link>
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
