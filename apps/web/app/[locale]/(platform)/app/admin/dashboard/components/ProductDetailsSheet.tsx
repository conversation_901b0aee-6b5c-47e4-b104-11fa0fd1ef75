'use client';

import { useState } from 'react';
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@ui/components/sheet';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { formatCurrency } from 'utils';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ExternalLink, Eye, TrendingUp, Users, DollarSign } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface ProductDetailsSheetProps {
  product: {
    id: string;
    title: string;
    description?: string;
    thumbnail?: string | null;
    type: string;
    status?: string;
    price?: number;
    totalSales: number;
    revenue: number;
    totalStudents?: number;
    rating?: number;
    totalReviews?: number;
    createdAt?: string;
    updatedAt?: string;
  } & Record<string, any>;
  children: React.ReactNode;
}

export function ProductDetailsSheet({ product, children }: ProductDetailsSheetProps) {
  const [open, setOpen] = useState(false);

  const statusColors = {
    DRAFT: 'bg-gray-500',
    PUBLISHED: 'bg-green-500',
    ARCHIVED: 'bg-red-500',
  };

  const typeLabels = {
    COURSE: 'Curso',
    EBOOK: 'E-book',
    MENTORING: 'Mentoria',
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Detalhes do Produto
          </SheetTitle>
        </SheetHeader>
        
        <div className="mt-6 space-y-6">
          {/* Header do Produto */}
          <div className="flex items-start gap-4">
            {product.thumbnail && (
              <div className="relative h-20 w-20 overflow-hidden rounded-lg">
                <Image
                  src={product.thumbnail}
                  alt={product.title}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="flex-1">
              <h3 className="font-semibold text-lg">{product.title}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">
                  {typeLabels[product.type as keyof typeof typeLabels] || product.type}
                </Badge>
                {product.status && (
                  <>
                    <div
                      className={`h-2 w-2 rounded-full ${
                        statusColors[product.status as keyof typeof statusColors]
                      }`}
                    />
                    <span className="text-sm text-muted-foreground capitalize">
                      {product.status.toLowerCase()}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>

          {product.description && (
            <div>
              <h4 className="font-medium mb-2">Descrição</h4>
              <p className="text-sm text-muted-foreground line-clamp-3">
                {product.description}
              </p>
            </div>
          )}

          <Separator />

          {/* Estatísticas de Vendas */}
          <div>
            <h4 className="font-medium mb-4">Estatísticas de Vendas</h4>
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Total de Vendas</p>
                      <p className="text-2xl font-bold">{product.totalSales}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Receita Total</p>
                      <p className="text-2xl font-bold">{formatCurrency(product.revenue)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Informações do Produto */}
          <div>
            <h4 className="font-medium mb-4">Informações do Produto</h4>
            <div className="space-y-3">
              {product.price && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Preço</span>
                  <span className="font-medium">{formatCurrency(product.price)}</span>
                </div>
              )}
              
              {product.totalStudents && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Estudantes</span>
                  <span className="font-medium">{product.totalStudents}</span>
                </div>
              )}
              
              {product.rating && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Avaliação</span>
                  <div className="flex items-center gap-1">
                    <span className="font-medium">{product.rating.toFixed(1)}</span>
                    <span className="text-sm text-muted-foreground">
                      ({product.totalReviews} avaliações)
                    </span>
                  </div>
                </div>
              )}
              
              {product.createdAt && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Criado em</span>
                  <span className="text-sm">
                    {format(new Date(product.createdAt), 'dd/MM/yyyy', { locale: ptBR })}
                  </span>
                </div>
              )}
              
              {product.updatedAt && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Atualizado em</span>
                  <span className="text-sm">
                    {format(new Date(product.updatedAt), 'dd/MM/yyyy', { locale: ptBR })}
                  </span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Ações */}
          <div className="flex gap-2">
            <Button asChild variant="outline" className="flex-1">
              <Link href={`/app/products/${product.id}`}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Ver Produto
              </Link>
            </Button>
            <Button asChild className="flex-1">
              <Link href={`/app/admin/products/${product.id}`}>
                <Eye className="h-4 w-4 mr-2" />
                Gerenciar
              </Link>
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
