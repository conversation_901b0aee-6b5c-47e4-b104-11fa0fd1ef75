'use client';

import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@ui/components/table';
import {
	BookOpen,
	DollarSign,
	ShoppingCart,
	Users,
	TrendingUp,
	FileText,
	GraduationCap,
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatCurrency } from 'utils';
import Image from 'next/image';
import { Skeleton } from '@ui/components/skeleton';
import { ProductDetailsSheet } from './components/ProductDetailsSheet';
import { OrderDetailsSheet } from './components/OrderDetailsSheet';
import { Button } from '@ui/components/button';
import { Eye } from 'lucide-react';
import {
	Area,
	AreaChart,
	ResponsiveContainer,
	XAxis,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>ltip,
	CartesianGrid,
} from 'recharts';

export default function AdminDashboardPage() {
	const { data: stats, isLoading } = apiClient.admin.getDashboardStats.useQuery();

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div>
					<h1 className="text-3xl font-bold">Dashboard Administrativo</h1>
					<p className="text-muted-foreground">
						Visão geral completa da plataforma
					</p>
				</div>

				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					{[...Array(8)].map((_, i) => (
						<Skeleton key={i} className="h-32" />
					))}
				</div>
			</div>
		);
	}

	if (!stats) return null;

	const orderStatusColors = {
		PENDING: 'bg-yellow-500',
		PROCESSING: 'bg-blue-500',
		PAID: 'bg-green-500',
		FAILED: 'bg-red-500',
		REFUNDED: 'bg-gray-500',
		CANCELLED: 'bg-gray-400',
	};

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Dashboard Administrativo</h1>
				<p className="text-muted-foreground">
					Visão geral completa da plataforma
				</p>
			</div>

			{/* Estatísticas Gerais */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Produtos</CardTitle>
						<BookOpen className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalProducts}</div>
						<p className="text-xs text-muted-foreground">
							{stats.monthlyStats.newProducts} novos este mês
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Pedidos</CardTitle>
						<ShoppingCart className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalOrders}</div>
						<p className="text-xs text-muted-foreground">
							{stats.monthlyStats.newOrders} novos este mês
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Total</CardTitle>
						<DollarSign className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{formatCurrency(stats.totalRevenue)}
						</div>
						<p className="text-xs text-muted-foreground">
							{formatCurrency(stats.monthlyStats.newRevenue)} este mês
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Usuários</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalUsers}</div>
						<p className="text-xs text-muted-foreground">
							{stats.monthlyStats.newUsers} novos este mês
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Estatísticas de Produtos */}
			<div className="grid gap-4 md:grid-cols-3">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Cursos</CardTitle>
						<GraduationCap className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalCourses}</div>
						<p className="text-xs text-muted-foreground">
							{stats.totalActiveEnrollments} matrículas ativas
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Ebooks</CardTitle>
						<FileText className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalEbooks}</div>
						<p className="text-xs text-muted-foreground">
							{stats.totalEbookPurchases} compras ativas
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Mentorias</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalMentorings}</div>
						<p className="text-xs text-muted-foreground">Produtos disponíveis</p>
					</CardContent>
				</Card>
			</div>

			{/* Gráfico de Crescimento */}
			<Card>
				<CardHeader>
					<CardTitle>Crescimento (Últimos 30 Dias)</CardTitle>
					<CardDescription>
						Vendas e receita diária
					</CardDescription>
				</CardHeader>
				<CardContent>
					<ResponsiveContainer width="100%" height={300}>
						<AreaChart data={stats.growthChart}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis
								dataKey="date"
								tickFormatter={(value) =>
									format(new Date(value), 'dd/MM', { locale: ptBR })
								}
							/>
							<YAxis yAxisId="left" />
							<YAxis yAxisId="right" orientation="right" />
							<Tooltip
								labelFormatter={(value) =>
									format(new Date(value as string), 'dd/MM/yyyy', {
										locale: ptBR,
									})
								}
								formatter={(value: any) =>
									typeof value === 'number'
										? value >= 1000
											? formatCurrency(value)
											: value
										: value
								}
							/>
							<Area
								yAxisId="left"
								type="monotone"
								dataKey="orders"
								stroke="#8884d8"
								fill="#8884d8"
								name="Pedidos"
							/>
							<Area
								yAxisId="right"
								type="monotone"
								dataKey="revenue"
								stroke="#82ca9d"
								fill="#82ca9d"
								name="Receita (R$)"
							/>
						</AreaChart>
					</ResponsiveContainer>
				</CardContent>
			</Card>

			<div className="grid gap-4 md:grid-cols-2">
				{/* Produtos Mais Vendidos */}
				<Card>
					<CardHeader>
						<CardTitle>Produtos Mais Vendidos</CardTitle>
						<CardDescription>Top 5 dos últimos 30 dias</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{stats.topSellingProducts.map((product, index) => (
								<ProductDetailsSheet key={product.id} product={product}>
									<div className="flex items-center gap-3 cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors">
										<div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-sm font-bold">
											{index + 1}
										</div>
										{product.thumbnail && (
											<div className="relative h-12 w-12 overflow-hidden rounded">
												<Image
													src={product.thumbnail}
													alt={product.title}
													fill
													className="object-cover"
												/>
											</div>
										)}
										<div className="flex-1">
											<p className="font-medium text-sm">{product.title}</p>
											<div className="flex items-center gap-2 text-xs text-muted-foreground">
												<span className="text-xs border rounded px-2 py-1 bg-gray-100">
													{product.type}
												</span>
												<span>{product.totalSales} vendas</span>
											</div>
										</div>
										<div className="text-right">
											<p className="font-semibold text-sm">
												{formatCurrency(product.revenue)}
											</p>
										</div>
									</div>
								</ProductDetailsSheet>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Pedidos Recentes */}
				<Card>
					<CardHeader>
						<CardTitle>Pedidos Recentes</CardTitle>
						<CardDescription>Últimas 10 transações</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							{stats.recentOrders.map((order) => (
								<OrderDetailsSheet key={order.id} order={order}>
									<div className="flex items-center gap-3 cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors">
										<div
											className={`h-2 w-2 rounded-full ${
												orderStatusColors[order.status]
											}`}
										/>
										<div className="flex-1">
											<p className="font-medium text-sm">
												{order.user.name || order.user.email}
											</p>
											<p className="text-xs text-muted-foreground">
												{order.product.title}
											</p>
										</div>
										<div className="text-right">
											<p className="font-semibold text-sm">
												{formatCurrency(order.amount)}
											</p>
											<p className="text-xs text-muted-foreground">
												{format(new Date(order.createdAt), 'dd/MM/yy HH:mm', {
													locale: ptBR,
												})}
											</p>
										</div>
									</div>
								</OrderDetailsSheet>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

