'use client';

import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Textarea } from '@ui/components/textarea';
import { Label } from '@ui/components/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@ui/components/table';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from '@ui/components/dialog';
import { Skeleton } from '@ui/components/skeleton';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
	SearchIcon, 
	UserPlus, 
	XCircle, 
	CheckCircle,
	AlertCircle,
	Loader2,
	Mail
} from 'lucide-react';
import { useToast } from '@ui/hooks/use-toast';

export default function AdminEnrollmentsPage() {
	const [search, setSearch] = useState('');
	const [productType, setProductType] = useState<string>('');
	const [status, setStatus] = useState<string>('');
	const [page, setPage] = useState(1);
	
	// Estados para liberação manual com dados de usuário
	const [showGrantAccessModal, setShowGrantAccessModal] = useState(false);
	
	// Dados do usuário
	const [userName, setUserName] = useState('');
	const [userEmail, setUserEmail] = useState('');
	const [userCpf, setUserCpf] = useState('');
	
	// Dados do produto
	const [selectedProductId, setSelectedProductId] = useState('');
	const [productSearchQuery, setProductSearchQuery] = useState('');
	const [selectedProduct, setSelectedProduct] = useState<any>(null);
	
	// Configurações
	const [accessReason, setAccessReason] = useState('');
	const [maxDownloads, setMaxDownloads] = useState(5);
	const [expiresInDays, setExpiresInDays] = useState<number | undefined>();
	const [notifyUser, setNotifyUser] = useState(true);

	// Estados para revogação
	const [showRevokeModal, setShowRevokeModal] = useState(false);
	const [revokeAccessData, setRevokeAccessData] = useState<{
		userId: string;
		productId: string;
		productType: 'COURSE' | 'EBOOK' | 'MENTORING';
		userName: string;
		productName: string;
	} | null>(null);
	const [revokeReason, setRevokeReason] = useState('');

	// Estados para reenvio de email
	const [showResendModal, setShowResendModal] = useState(false);
	const [resendEmailData, setResendEmailData] = useState<{
		enrollmentId: string;
		enrollmentType: 'COURSE' | 'EBOOK' | 'MENTORING';
		userName: string;
		productName: string;
	} | null>(null);

	const { toast } = useToast();

	const { data, isLoading, refetch } = apiClient.admin.listEnrollments.useQuery({
		search: search || undefined,
		productType: productType && productType !== 'all' ? (productType as any) : undefined,
		status: status && status !== 'all' ? (status as any) : undefined,
		page,
		pageSize: 20,
	});

	// Buscar produtos
	const { data: products, isLoading: isLoadingProducts } = apiClient.admin.listAllProducts.useQuery(
		{
			search: productSearchQuery,
			status: 'PUBLISHED',
			page: 1,
			pageSize: 20,
		},
		{ enabled: productSearchQuery.length >= 2 }
	);

	// Mutation para liberar acesso com dados de usuário
	const grantAccessMutation = apiClient.admin.grantAccessWithUserData.useMutation({
		onSuccess: (data) => {
			toast({
				title: 'Sucesso!',
				description: data.message,
			});
			setShowGrantAccessModal(false);
			resetGrantAccessForm();
			refetch();
		},
		onError: (error) => {
			toast({
				title: 'Erro',
				description: error.message,
			});
		},
	});

	const revokeAccessMutation = apiClient.admin.revokeAccess.useMutation({
		onSuccess: () => {
			toast({
				title: 'Sucesso!',
				description: 'Acesso revogado com sucesso',
			});
			setShowRevokeModal(false);
			setRevokeAccessData(null);
			setRevokeReason('');
			refetch();
		},
		onError: (error) => {
			toast({
				title: 'Erro',
				description: error.message,
			});
		},
	});

	const resendEmailMutation = apiClient.admin.resendAccessEmail.useMutation({
		onSuccess: (data) => {
			toast({
				title: 'Sucesso!',
				description: data.message,
			});
			setShowResendModal(false);
			setResendEmailData(null);
		},
		onError: (error) => {
			toast({
				title: 'Erro',
				description: error.message,
			});
		},
	});

	const resetGrantAccessForm = () => {
		setUserName('');
		setUserEmail('');
		setUserCpf('');
		setSelectedProductId('');
		setSelectedProduct(null);
		setAccessReason('');
		setMaxDownloads(5);
		setExpiresInDays(undefined);
		setNotifyUser(true);
		setProductSearchQuery('');
	};

	const handleGrantAccess = () => {
		// Validar campos obrigatórios
		if (!userName || !userEmail) {
			toast({
				title: 'Erro',
				description: 'Nome e email são obrigatórios',
			});
			return;
		}

		if (!selectedProductId) {
			toast({
				title: 'Erro',
				description: 'Selecione um produto',
			});
			return;
		}

		// Chamar mutation
		grantAccessMutation.mutate({
			name: userName,
			email: userEmail,
			cpf: userCpf || undefined,
			productId: selectedProductId,
			accessType: selectedProduct?.type || 'COURSE',
			maxDownloads: selectedProduct?.type === 'EBOOK' ? maxDownloads : undefined,
			expiresInDays,
			reason: accessReason || undefined,
			notifyUser,
		});
	};

	const handleRevokeAccess = () => {
		if (!revokeAccessData || !revokeReason) {
			toast({
				title: 'Erro',
				description: 'Informe o motivo da revogação',
			});
			return;
		}

		revokeAccessMutation.mutate({
			userId: revokeAccessData.userId,
			productId: revokeAccessData.productId,
			productType: revokeAccessData.productType,
			reason: revokeReason,
			notifyUser: true,
		});
	};

	const handleResendEmail = () => {
		if (!resendEmailData) {
			toast({
				title: 'Erro',
				description: 'Dados de reenvio não encontrados',
			});
			return;
		}

		resendEmailMutation.mutate({
			enrollmentId: resendEmailData.enrollmentId,
			enrollmentType: resendEmailData.enrollmentType,
		});
	};

	const productTypeLabels = {
		COURSE: 'Curso',
		EBOOK: 'Ebook',
		MENTORING: 'Mentoria',
	};

	const statusColors = {
		ACTIVE: 'bg-green-500',
		INACTIVE: 'bg-gray-500',
		SUSPENDED: 'bg-orange-500',
		COMPLETED: 'bg-blue-500',
		CANCELLED: 'bg-red-500',
		EXPIRED: 'bg-gray-400',
		REVOKED: 'bg-red-600',
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Gerenciar Acessos</h1>
					<p className="text-muted-foreground">
						Visualize e gerencie matrículas e acessos a produtos
					</p>
				</div>
				<Button onClick={() => setShowGrantAccessModal(true)}>
					<UserPlus className="mr-2 h-4 w-4" />
					Liberar Acesso Manual
				</Button>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Filtros</CardTitle>
					<CardDescription>
						Busque e filtre acessos por critérios específicos
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-3">
						<div className="relative">
							<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
							<Input
								placeholder="Buscar por email, nome..."
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								className="pl-10"
							/>
						</div>

						<Select value={productType} onValueChange={setProductType}>
							<SelectTrigger>
								<SelectValue placeholder="Tipo de produto" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Todos os tipos</SelectItem>
								<SelectItem value="COURSE">Cursos</SelectItem>
								<SelectItem value="EBOOK">Ebooks</SelectItem>
							</SelectContent>
						</Select>

						<Select value={status} onValueChange={setStatus}>
							<SelectTrigger>
								<SelectValue placeholder="Status" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Status</SelectItem>
								<SelectItem value="ACTIVE">Ativo</SelectItem>
								<SelectItem value="INACTIVE">Inativo</SelectItem>
								<SelectItem value="SUSPENDED">Suspenso</SelectItem>
								<SelectItem value="COMPLETED">Completo</SelectItem>
								<SelectItem value="CANCELLED">Cancelado</SelectItem>
								<SelectItem value="EXPIRED">Expirado</SelectItem>
								<SelectItem value="REVOKED">Revogado</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Acessos</CardTitle>
					<CardDescription>
						{data?.pagination.total || 0} acessos encontrados
					</CardDescription>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-3">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-20" />
							))}
						</div>
					) : (
						<>
							<div className="overflow-x-auto">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Usuário</TableHead>
											<TableHead>Produto</TableHead>
											<TableHead>Tipo</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Detalhes</TableHead>
											<TableHead>Data</TableHead>
											<TableHead className="text-right">Ações</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{data?.enrollments.map((enrollment) => (
											<TableRow key={enrollment.id}>
												<TableCell>
													<div>
														<p className="font-medium">
															{enrollment.user.name || enrollment.user.email}
														</p>
														<p className="text-xs text-muted-foreground">
															{enrollment.user.email}
														</p>
													</div>
												</TableCell>
												<TableCell>
													<p className="font-medium">{enrollment.product.title}</p>
												</TableCell>
												<TableCell>
												<Badge>
													{productTypeLabels[enrollment.type] || enrollment.type}
												</Badge>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<div
															className={`h-2 w-2 rounded-full ${
																statusColors[enrollment.status] || 'bg-gray-500'
															}`}
														/>
														{enrollment.status}
													</div>
												</TableCell>
												<TableCell>
													{enrollment.type === 'COURSE' && (
														<p className="text-sm">
															Progresso: {enrollment.progress}%
														</p>
													)}
													{enrollment.type === 'EBOOK' && enrollment.details && (
														<p className="text-sm">
															Downloads: {enrollment.details.downloadCount}/
															{enrollment.details.maxDownloads}
														</p>
													)}
												</TableCell>
												<TableCell>
													<div>
														<p className="text-sm">
															{format(
																new Date(enrollment.createdAt),
																'dd/MM/yyyy',
																{ locale: ptBR }
															)}
														</p>
														<p className="text-xs text-muted-foreground">
															Último acesso:{' '}
															{format(
																new Date(enrollment.lastAccessedAt),
																'dd/MM/yy',
																{ locale: ptBR }
															)}
														</p>
													</div>
												</TableCell>
												<TableCell className="text-right">
													<div className="flex gap-2 justify-end">
														{['ACTIVE', 'INACTIVE'].includes(enrollment.status) && (
															<Button
																variant="outline"
																size="sm"
																onClick={() => {
																	setResendEmailData({
																		enrollmentId: enrollment.id,
																		enrollmentType: enrollment.type,
																		userName:
																			enrollment.user.name ||
																			enrollment.user.email,
																		productName: enrollment.product.title,
																	});
																	setShowResendModal(true);
																}}
															>
																<Mail className="mr-2 h-4 w-4" />
																Reenviar Email
															</Button>
														)}
														{['ACTIVE', 'INACTIVE'].includes(enrollment.status) && (
															<Button
																variant="outline"
																size="sm"
																onClick={() => {
																	setRevokeAccessData({
																		userId: enrollment.userId,
																		productId: enrollment.productId,
																		productType: enrollment.type,
																		userName:
																			enrollment.user.name ||
																			enrollment.user.email,
																		productName: enrollment.product.title,
																	});
																	setShowRevokeModal(true);
																}}
															>
																<XCircle className="mr-2 h-4 w-4" />
																Revogar
															</Button>
														)}
													</div>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{data && data.pagination.totalPages > 1 && (
								<div className="mt-4 flex items-center justify-between">
									<p className="text-sm text-muted-foreground">
										Página {data.pagination.page} de {data.pagination.totalPages}
									</p>
									<div className="flex gap-2">
										<Button
											variant="outline"
											size="sm"
											disabled={page === 1}
											onClick={() => setPage(page - 1)}
										>
											Anterior
										</Button>
										<Button
											variant="outline"
											size="sm"
											disabled={page === data.pagination.totalPages}
											onClick={() => setPage(page + 1)}
										>
											Próxima
										</Button>
									</div>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>

			{/* Modal de Liberação Manual - Novo Fluxo */}
			<Dialog open={showGrantAccessModal} onOpenChange={setShowGrantAccessModal}>
				<DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Liberar Acesso Manual</DialogTitle>
						<DialogDescription>
							Informe os dados do usuário e selecione o produto. Se o usuário não existir, será criado automaticamente.
						</DialogDescription>
					</DialogHeader>

					<div className="space-y-6">
						{/* Seção 1: Dados do Usuário */}
						<div className="space-y-4">
							<div className="border-b pb-2">
								<h3 className="font-semibold text-sm">Dados do Usuário</h3>
								<p className="text-xs text-muted-foreground">
									Se o usuário não existir, será criado automaticamente
								</p>
							</div>

							<div className="grid gap-4 md:grid-cols-2">
								<div>
									<Label>Nome Completo *</Label>
									<Input
										placeholder="Digite o nome completo..."
										value={userName}
										onChange={(e) => setUserName(e.target.value)}
									/>
								</div>

								<div>
									<Label>Email *</Label>
									<Input
										type="email"
										placeholder="Digite o email..."
										value={userEmail}
										onChange={(e) => setUserEmail(e.target.value)}
									/>
								</div>

								<div>
									<Label>CPF (opcional)</Label>
									<Input
										placeholder="Digite o CPF..."
										value={userCpf}
										onChange={(e) => setUserCpf(e.target.value)}
									/>
								</div>
							</div>
						</div>

						{/* Seção 2: Produto */}
						<div className="space-y-4">
							<div className="border-b pb-2">
								<h3 className="font-semibold text-sm">Produto</h3>
								<p className="text-xs text-muted-foreground">
									Busque e selecione o produto
								</p>
							</div>

							<div>
								<Label>Buscar Produto *</Label>
								<div className="relative">
									<Input
										placeholder="Digite o nome do produto (mínimo 2 caracteres)..."
										value={productSearchQuery}
										onChange={(e) => setProductSearchQuery(e.target.value)}
									/>
									{isLoadingProducts && (
										<div className="absolute right-3 top-1/2 -translate-y-1/2">
											<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
										</div>
									)}
								</div>
								
								{/* Produto Selecionado */}
								{selectedProduct && (
									<div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md dark:bg-green-950 dark:border-green-800">
										<div className="flex items-center justify-between">
											<div className="flex-1">
												<p className="font-medium text-sm text-green-900 dark:text-green-100">
													{selectedProduct.title}
												</p>
												<Badge className="text-xs mt-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
													{productTypeLabels[selectedProduct.type] || selectedProduct.type}
												</Badge>
											</div>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => {
													setSelectedProductId('');
													setSelectedProduct(null);
													setProductSearchQuery('');
												}}
												className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
											>
												<XCircle className="h-4 w-4" />
											</Button>
										</div>
									</div>
								)}

								{/* Lista de Produtos */}
								{!selectedProduct && productSearchQuery.length >= 2 && (
									<div className="mt-2">
										{isLoadingProducts ? (
											<div className="p-4 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-950 dark:border-blue-800">
												<div className="flex items-center gap-2">
													<Loader2 className="h-4 w-4 animate-spin text-blue-600" />
													<p className="text-sm text-blue-800 dark:text-blue-200">
														Buscando produtos...
													</p>
												</div>
											</div>
										) : products && products.products.length > 0 ? (
											<div className="max-h-40 overflow-y-auto rounded-md border">
												{products.products.map((product) => (
													<div
														key={product.id}
														className="cursor-pointer p-3 hover:bg-muted border-b last:border-b-0"
														onClick={() => {
															setSelectedProductId(product.id);
															setSelectedProduct(product);
															setProductSearchQuery(product.title);
														}}
													>
														<div className="flex items-center justify-between">
															<div className="flex-1">
																<p className="font-medium text-sm">{product.title}</p>
																<Badge className="text-xs mt-1">
																	{productTypeLabels[product.type as keyof typeof productTypeLabels]}
																</Badge>
															</div>
															<CheckCircle className="h-4 w-4 text-muted-foreground" />
														</div>
													</div>
												))}
											</div>
										) : (
											<div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md dark:bg-yellow-950 dark:border-yellow-800">
												<p className="text-sm text-yellow-800 dark:text-yellow-200">
													Nenhum produto encontrado para "{productSearchQuery}"
												</p>
											</div>
										)}
									</div>
								)}

								{/* Mensagem de Instrução */}
								{!selectedProduct && productSearchQuery.length > 0 && productSearchQuery.length < 2 && (
									<div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-950 dark:border-blue-800">
										<p className="text-sm text-blue-800 dark:text-blue-200">
											Digite pelo menos 2 caracteres para buscar
										</p>
									</div>
								)}
							</div>
						</div>

						{/* Seção 3: Configurações Específicas para Ebook */}
						{selectedProduct?.type === 'EBOOK' && (
							<div className="space-y-4">
								<div className="border-b pb-2">
									<h3 className="font-semibold text-sm">Configurações do Ebook</h3>
								</div>

								<div className="grid gap-4 md:grid-cols-2">
									<div>
										<Label>Máximo de Downloads</Label>
										<Input
											type="number"
											min={1}
											max={100}
											value={maxDownloads}
											onChange={(e) => setMaxDownloads(Number(e.target.value))}
										/>
									</div>
									<div>
										<Label>Expira em (dias) - Opcional</Label>
										<Input
											type="number"
											min={1}
											placeholder="Deixe em branco para não expirar"
											value={expiresInDays || ''}
											onChange={(e) =>
												setExpiresInDays(
													e.target.value ? Number(e.target.value) : undefined
												)
											}
										/>
									</div>
								</div>
							</div>
						)}

						{/* Seção 4: Configurações Gerais */}
						<div className="space-y-4">
							<div className="border-b pb-2">
								<h3 className="font-semibold text-sm">Configurações Adicionais</h3>
							</div>

							<div>
								<Label>Motivo (opcional)</Label>
								<Textarea
									placeholder="Ex: Promoção especial, cortesia, reembolso processado..."
									value={accessReason}
									onChange={(e) => setAccessReason(e.target.value)}
									rows={3}
								/>
							</div>

							<div className="flex items-center space-x-2">
								<input
									type="checkbox"
									id="notifyUser"
									checked={notifyUser}
									onChange={(e) => setNotifyUser(e.target.checked)}
									className="rounded"
								/>
								<Label htmlFor="notifyUser">
									Enviar email de notificação ao usuário
								</Label>
							</div>
						</div>
					</div>

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowGrantAccessModal(false);
								resetGrantAccessForm();
							}}
						>
							Cancelar
						</Button>
						<Button
							onClick={handleGrantAccess}
							disabled={
								!userName ||
								!userEmail ||
								!selectedProductId ||
								grantAccessMutation.isPending
							}
						>
							<CheckCircle className="mr-2 h-4 w-4" />
							{grantAccessMutation.isPending ? 'Processando...' : 'Liberar Acesso'}
						</Button>
						
						
					</DialogFooter>

					 
				</DialogContent>
			</Dialog>

			{/* Modal de Revogação */}
			<Dialog open={showRevokeModal} onOpenChange={setShowRevokeModal}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Revogar Acesso</DialogTitle>
						<DialogDescription>
							Esta ação cancelará o acesso do usuário ao produto
						</DialogDescription>
					</DialogHeader>

					{revokeAccessData && (
						<div className="space-y-4">
							<div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950">
								<div className="flex items-start gap-2">
									<AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
									<div>
										<p className="font-medium text-sm text-yellow-900 dark:text-yellow-100">
											Atenção
										</p>
										<p className="text-sm text-yellow-800 dark:text-yellow-200">
											Você está prestes a revogar o acesso de{' '}
											<strong>{revokeAccessData.userName}</strong> ao produto{' '}
											<strong>{revokeAccessData.productName}</strong>
										</p>
									</div>
								</div>
							</div>

							<div>
								<Label>Motivo da Revogação *</Label>
								<Textarea
									placeholder="Explique o motivo da revogação..."
									value={revokeReason}
									onChange={(e) => setRevokeReason(e.target.value)}
									rows={3}
									required
								/>
							</div>
						</div>
					)}

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowRevokeModal(false);
								setRevokeAccessData(null);
								setRevokeReason('');
							}}
						>
							Cancelar
						</Button>
						<Button
							onClick={handleRevokeAccess}
							disabled={!revokeReason || revokeAccessMutation.isPending}
							className="bg-red-600 hover:bg-red-700"
						>
							<XCircle className="mr-2 h-4 w-4" />
							Revogar Acesso
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Modal de Reenvio de Email */}
			<Dialog open={showResendModal} onOpenChange={setShowResendModal}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Reenviar Email de Acesso</DialogTitle>
						<DialogDescription>
							Um novo email com link de acesso será enviado ao usuário
						</DialogDescription>
					</DialogHeader>

					{resendEmailData && (
						<div className="space-y-4">
							<div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950">
								<div className="flex items-start gap-2">
									<Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
									<div>
										<p className="font-medium text-sm text-blue-900 dark:text-blue-100">
											Reenviar Email de Acesso
										</p>
										<p className="text-sm text-blue-800 dark:text-blue-200">
											Um novo email com magic link será enviado para{' '}
											<strong>{resendEmailData.userName}</strong> com acesso ao produto{' '}
											<strong>{resendEmailData.productName}</strong>
										</p>
									</div>
								</div>
							</div>

							<div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950">
								<div className="flex items-start gap-2">
									<AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
									<div>
										<p className="font-medium text-sm text-yellow-900 dark:text-yellow-100">
											Importante
										</p>
										<p className="text-sm text-yellow-800 dark:text-yellow-200">
											O email conterá um link de acesso direto e um código OTP como alternativa.
											O link expira em 48 horas.
										</p>
									</div>
								</div>
							</div>
						</div>
					)}

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowResendModal(false);
								setResendEmailData(null);
							}}
						>
							Cancelar
						</Button>
						<Button
							onClick={handleResendEmail}
							disabled={resendEmailMutation.isPending}
						>
							<Mail className="mr-2 h-4 w-4" />
							{resendEmailMutation.isPending ? 'Enviando...' : 'Reenviar Email'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
