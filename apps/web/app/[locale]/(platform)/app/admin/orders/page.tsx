'use client';

import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@ui/components/table';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
} from '@ui/components/dialog';
import { Skeleton } from '@ui/components/skeleton';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatCurrency } from 'utils';
import { SearchIcon, Eye, CheckCircle, XCircle, Clock } from 'lucide-react';

export default function AdminOrdersPage() {
	const [search, setSearch] = useState('');
	const [status, setStatus] = useState<string>('');
	const [page, setPage] = useState(1);
	const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);

	const { data, isLoading } = apiClient.admin.listAllOrders.useQuery({
		search: search || undefined,
		status: status && status !== 'all' ? (status as any) : undefined,
		page,
		pageSize: 20,
	});

	const { data: orderDetails, isLoading: isLoadingDetails } =
		apiClient.admin.getOrderDetails.useQuery(
			{ orderId: selectedOrderId! },
			{ enabled: !!selectedOrderId }
		);

	const orderStatusLabels = {
		PENDING: 'Pendente',
		PROCESSING: 'Processando',
		PAID: 'Pago',
		FAILED: 'Falhou',
		REFUNDED: 'Reembolsado',
		CANCELLED: 'Cancelado',
	};

	const orderStatusColors = {
		PENDING: 'bg-yellow-500',
		PROCESSING: 'bg-blue-500',
		PAID: 'bg-green-500',
		FAILED: 'bg-red-500',
		REFUNDED: 'bg-gray-500',
		CANCELLED: 'bg-gray-400',
	};

	const productTypeLabels = {
		COURSE: 'Curso',
		EBOOK: 'Ebook',
		MENTORING: 'Mentoria',
	};

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Gerenciar Pedidos</h1>
				<p className="text-muted-foreground">
					Visualize e gerencie todos os pedidos da plataforma
				</p>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Filtros</CardTitle>
					<CardDescription>
						Busque e filtre pedidos por critérios específicos
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-2">
						<div className="relative">
							<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
							<Input
								placeholder="Buscar por email, nome, CPF..."
								value={search}
								onChange={(e) => setSearch(e.target.value)}
								className="pl-10"
							/>
						</div>

						<Select value={status} onValueChange={setStatus}>
							<SelectTrigger>
								<SelectValue placeholder="Status do pedido" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Status</SelectItem>
								<SelectItem value="PENDING">Pendente</SelectItem>
								<SelectItem value="PROCESSING">Processando</SelectItem>
								<SelectItem value="PAID">Pago</SelectItem>
								<SelectItem value="FAILED">Falhou</SelectItem>
								<SelectItem value="REFUNDED">Reembolsado</SelectItem>
								<SelectItem value="CANCELLED">Cancelado</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Pedidos</CardTitle>
					<CardDescription>
						{data?.pagination.total || 0} pedidos encontrados
					</CardDescription>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-3">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-20" />
							))}
						</div>
					) : (
						<>
							<div className="overflow-x-auto">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Cliente</TableHead>
											<TableHead>Produto</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Valor</TableHead>
											<TableHead>Data</TableHead>
											<TableHead className="text-right">Ações</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{data?.orders.map((order) => (
											<TableRow key={order.id}>
												<TableCell>
													<div>
														<p className="font-medium">
															{order.user.name || order.user.email}
														</p>
														<p className="text-xs text-muted-foreground">
															{order.user.email}
														</p>
													</div>
												</TableCell>
												<TableCell>
													<div>
														<p className="font-medium">{order.product.title}</p>
														<div className="flex items-center gap-2">
															<Badge className="text-xs border">
																{productTypeLabels[order.product.type]}
															</Badge>
															{order.affiliate && (
																<Badge className="text-xs bg-gray-100 text-gray-800">
																	Com afiliado
																</Badge>
															)}
														</div>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<div
															className={`h-2 w-2 rounded-full ${
																orderStatusColors[order.status]
															}`}
														/>
														{orderStatusLabels[order.status]}
													</div>
												</TableCell>
												<TableCell>
													<div>
														<p className="font-medium">
															{formatCurrency(Number(order.amount))}
														</p>
														{order.installments > 1 && (
															<p className="text-xs text-muted-foreground">
																{order.installments}x
															</p>
														)}
													</div>
												</TableCell>
												<TableCell>
													<div>
														<p className="text-sm">
															{format(new Date(order.createdAt), 'dd/MM/yyyy', {
																locale: ptBR,
															})}
														</p>
														<p className="text-xs text-muted-foreground">
															{format(new Date(order.createdAt), 'HH:mm', {
																locale: ptBR,
															})}
														</p>
													</div>
												</TableCell>
												<TableCell className="text-right">
													<Button
														variant="outline"
														size="sm"
														onClick={() => setSelectedOrderId(order.id)}
													>
														<Eye className="mr-2 h-4 w-4" />
														Detalhes
													</Button>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{data && data.pagination.totalPages > 1 && (
								<div className="mt-4 flex items-center justify-between">
									<p className="text-sm text-muted-foreground">
										Página {data.pagination.page} de{' '}
										{data.pagination.totalPages}
									</p>
									<div className="flex gap-2">
										<Button
											variant="outline"
											size="sm"
											disabled={page === 1}
											onClick={() => setPage(page - 1)}
										>
											Anterior
										</Button>
										<Button
											variant="outline"
											size="sm"
											disabled={page === data.pagination.totalPages}
											onClick={() => setPage(page + 1)}
										>
											Próxima
										</Button>
									</div>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>

			{/* Modal de Detalhes do Pedido */}
			<Dialog open={!!selectedOrderId} onOpenChange={() => setSelectedOrderId(null)}>
				<DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Detalhes do Pedido</DialogTitle>
						<DialogDescription>
							Informações completas sobre o pedido
						</DialogDescription>
					</DialogHeader>

					{isLoadingDetails ? (
						<div className="space-y-4">
							<Skeleton className="h-20" />
							<Skeleton className="h-20" />
							<Skeleton className="h-20" />
						</div>
					) : orderDetails ? (
						<div className="space-y-6">
							{/* Status e Informações Básicas */}
							<div className="grid gap-4 md:grid-cols-2">
								<div>
									<h3 className="mb-2 font-semibold">Status do Pedido</h3>
									<div className="flex items-center gap-2">
										<div
											className={`h-3 w-3 rounded-full ${
												orderStatusColors[orderDetails.status]
											}`}
										/>
										<span className="text-lg">
											{orderStatusLabels[orderDetails.status]}
										</span>
									</div>
								</div>
								<div>
									<h3 className="mb-2 font-semibold">Valor Total</h3>
									<p className="text-2xl font-bold">
										{formatCurrency(Number(orderDetails.amount))}
									</p>
									{orderDetails.installments > 1 && (
										<p className="text-sm text-muted-foreground">
											{orderDetails.installments}x de{' '}
											{formatCurrency(
												Number(orderDetails.amount) / orderDetails.installments
											)}
										</p>
									)}
								</div>
							</div>

							{/* Cliente */}
							<div>
								<h3 className="mb-2 font-semibold">Cliente</h3>
								<div className="rounded-lg border p-4">
									<p className="font-medium">
										{orderDetails.user.name || 'Nome não informado'}
									</p>
									<p className="text-sm text-muted-foreground">
										{orderDetails.user.email}
									</p>
									{orderDetails.user.cpf && (
										<p className="text-sm text-muted-foreground">
											CPF: {orderDetails.user.cpf}
										</p>
									)}
								</div>
							</div>

							{/* Produto */}
							<div>
								<h3 className="mb-2 font-semibold">Produto</h3>
								<div className="rounded-lg border p-4">
									<div className="flex items-center justify-between">
										<div>
											<p className="font-medium">{orderDetails.product.title}</p>
											<Badge className="mt-1 border">
												{productTypeLabels[orderDetails.product.type]}
											</Badge>
										</div>
									</div>
									<div className="mt-2 text-sm text-muted-foreground">
										Criador: {orderDetails.product.creator.name ||
											orderDetails.product.creator.email}
									</div>
								</div>
							</div>

							{/* Acesso Gerado */}
							{orderDetails.access && (
								<div>
									<h3 className="mb-2 font-semibold">Acesso ao Produto</h3>
									{orderDetails.access.courseEnrollment ? (
										<div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950">
											<div className="flex items-center gap-2 text-green-700 dark:text-green-300">
												<CheckCircle className="h-5 w-5" />
												<span className="font-medium">Matrícula Ativa</span>
											</div>
											<div className="mt-2 space-y-1 text-sm">
												<p>
													Progresso:{' '}
													{orderDetails.access.courseEnrollment.progress}%
												</p>
												<p>
													Último acesso:{' '}
													{format(
														new Date(
															orderDetails.access.courseEnrollment.lastAccessedAt
														),
														"dd/MM/yyyy 'às' HH:mm",
														{ locale: ptBR }
													)}
												</p>
											</div>
										</div>
									) : orderDetails.access.ebookPurchase ? (
										<div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950">
											<div className="flex items-center gap-2 text-green-700 dark:text-green-300">
												<CheckCircle className="h-5 w-5" />
												<span className="font-medium">Acesso ao Ebook Ativo</span>
											</div>
											<div className="mt-2 space-y-1 text-sm">
												<p>
													Downloads:{' '}
													{orderDetails.access.ebookPurchase.downloadCount}/
													{orderDetails.access.ebookPurchase.maxDownloads}
												</p>
												<p>
													Código de validação:{' '}
													{orderDetails.access.ebookPurchase.validationCode}
												</p>
											</div>
										</div>
									) : orderDetails.status === 'PAID' ? (
										<div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950">
											<div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
												<Clock className="h-5 w-5" />
												<span className="font-medium">
													Acesso ainda não criado
												</span>
											</div>
										</div>
									) : (
										<div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-800 dark:bg-gray-950">
											<div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
												<XCircle className="h-5 w-5" />
												<span className="font-medium">Sem acesso</span>
											</div>
										</div>
									)}
								</div>
							)}

							{/* Afiliado */}
							{orderDetails.affiliate && (
								<div>
									<h3 className="mb-2 font-semibold">Afiliado</h3>
									<div className="rounded-lg border p-4">
										<p className="font-medium">
											{orderDetails.affiliate.name || orderDetails.affiliate.email}
										</p>
										<p className="text-sm text-muted-foreground">
											{orderDetails.affiliate.email}
										</p>
									</div>
								</div>
							)}

							{/* Cupom */}
							{orderDetails.coupon && (
								<div>
									<h3 className="mb-2 font-semibold">Cupom Aplicado</h3>
									<div className="rounded-lg border p-4">
										<p className="font-medium">{orderDetails.coupon.code}</p>
										<p className="text-sm text-muted-foreground">
											{orderDetails.coupon.type === 'PERCENTAGE'
												? `${Number(orderDetails.coupon.value)}% de desconto`
												: `${formatCurrency(Number(orderDetails.coupon.value))} de desconto`}
										</p>
									</div>
								</div>
							)}

							{/* Datas */}
							<div className="grid gap-4 md:grid-cols-2">
								<div>
									<h3 className="mb-2 font-semibold">Criado em</h3>
									<p>
										{format(
											new Date(orderDetails.createdAt),
											"dd/MM/yyyy 'às' HH:mm",
											{ locale: ptBR }
										)}
									</p>
								</div>
								<div>
									<h3 className="mb-2 font-semibold">Atualizado em</h3>
									<p>
										{format(
											new Date(orderDetails.updatedAt),
											"dd/MM/yyyy 'às' HH:mm",
											{ locale: ptBR }
										)}
									</p>
								</div>
							</div>
						</div>
					) : null}
				</DialogContent>
			</Dialog>
		</div>
	);
}

