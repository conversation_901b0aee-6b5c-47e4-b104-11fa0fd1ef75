import { redirect } from 'next/navigation';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { ProductsForAffiliation } from './components/ProductsForAffiliation';

interface PageProps {
  searchParams: {
    search?: string;
    category?: string;
    page?: string;
  };
}

export default async function ProductsForAffiliationPage({ searchParams }: PageProps) {
  const session = await currentUser();

  if (!session?.user) {
    redirect('/auth/signin');
  }

  const page = parseInt(searchParams.page || '1');
  const limit = 12;
  const skip = (page - 1) * limit;

  // Construir filtros
  const where: any = {
    enableAffiliate: true,
    status: 'PUBLISHED',
    visibility: 'PUBLIC',
    // Excluir produtos que o usuário já é afiliado
    affiliateLinks: {
      none: {
        affiliate: {
          userId: session.user.id,
        },
      },
    },
    // Excluir produtos próprios do usuário
    creatorId: {
      not: session.user.id,
    },
  };

  if (searchParams.search) {
    where.OR = [
      { title: { contains: searchParams.search, mode: 'insensitive' } },
      { description: { contains: searchParams.search, mode: 'insensitive' } },
    ];
  }

  if (searchParams.category) {
    where.categoryId = searchParams.category;
  }

  const [products, total, categories] = await Promise.all([
    db.product.findMany({
      where,
      skip,
      take: limit,
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            affiliateLinks: true,
            orders: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    db.product.count({ where }),
    db.category.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' },
    }),
  ]);

  const totalPages = Math.ceil(total / limit);

  // Buscar produtos que o usuário já é afiliado
  const myAffiliateProducts = await db.affiliateLink.findMany({
    where: {
      affiliate: {
        userId: session.user.id,
      },
    },
    include: {
      product: {
        include: {
          creator: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });

  return (
    <ProductsForAffiliation
      products={products}
      categories={categories}
      myAffiliateProducts={myAffiliateProducts}
      pagination={{
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }}
      searchParams={searchParams}
    />
  );
}
