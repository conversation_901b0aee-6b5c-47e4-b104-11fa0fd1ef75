'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Badge } from '@ui/components/badge';
import {
  Search,
  Filter,
  Users,
  TrendingUp,
  DollarSign,
  Copy,
  Check,
  Plus
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface ProductsForAffiliationProps {
  products: Array<{
    id: string;
    title: string;
    description: string | null;
    thumbnail: string | null;
    price: number;
    affiliateCommission: number | null;
    category: {
      name: string;
    } | null;
    creator: {
      id: string;
      name: string | null;
      email: string;
    };
    _count: {
      affiliateLinks: number;
      orders: number;
    };
  }>;
  categories: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  myAffiliateProducts: Array<{
    id: string;
    code: string;
    clicks: number;
    conversions: number;
    product: {
      id: string;
      title: string;
      thumbnail: string | null;
      creator: {
        name: string | null;
        email: string;
      };
    };
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  searchParams: {
    search?: string;
    category?: string;
    page?: string;
  };
}

export function ProductsForAffiliation({
  products,
  categories,
  myAffiliateProducts,
  pagination,
  searchParams,
}: ProductsForAffiliationProps) {
  const [search, setSearch] = useState(searchParams.search || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.category || '');
  const [affiliatingProductId, setAffiliatingProductId] = useState<string | null>(null);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const router = useRouter();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const handleSearch = () => {
    const params = new URLSearchParams();
    if (search) params.set('search', search);
    if (selectedCategory) params.set('category', selectedCategory);
    params.set('page', '1');

    router.push(`/afiliados/produtos?${params.toString()}`);
  };

  const handleRequestAffiliation = async (productId: string) => {
    setAffiliatingProductId(productId);

    try {
      const response = await fetch('/api/affiliates/request-affiliation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao solicitar afiliação');
      }

      // Recarregar a página para mostrar o novo produto afiliado
      router.refresh();
    } catch (error) {
      console.error('Erro ao solicitar afiliação:', error);
      alert('Erro ao solicitar afiliação. Tente novamente.');
    } finally {
      setAffiliatingProductId(null);
    }
  };

  const copyAffiliateLink = async (code: string) => {
    const link = `${window.location.origin}/a/${code}`;
    await navigator.clipboard.writeText(link);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Produtos para Afiliação
        </h1>
        <p className="text-gray-600">
          Encontre produtos incríveis para promover e ganhar comissões
        </p>
      </div>

      {/* Meus Produtos de Afiliação */}
      {myAffiliateProducts.length > 0 && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Meus Produtos de Afiliação</CardTitle>
            <CardDescription>
              Produtos que você já está promovendo
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {myAffiliateProducts.map((affiliate) => (
                <div key={affiliate.id} className="flex items-center justify-between p-4 border rounded-lg bg-green-50">
                  <div className="flex items-center space-x-3">
                    {affiliate.product.thumbnail && (
                      <img
                        src={affiliate.product.thumbnail}
                        alt={affiliate.product.title}
                        className="w-10 h-10 object-cover rounded"
                      />
                    )}
                    <div>
                      <h4 className="font-medium text-sm">{affiliate.product.title}</h4>
                      <p className="text-xs text-gray-600">
                        {affiliate.clicks} cliques • {affiliate.conversions} vendas
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyAffiliateLink(affiliate.code)}
                  >
                    {copiedCode === affiliate.code ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filtros */}
      <Card className="mb-8">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar produtos..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <div className="md:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <Button onClick={handleSearch}>
              <Filter className="h-4 w-4 mr-2" />
              Filtrar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Produtos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {products.map((product) => (
          <Card key={product.id} className="overflow-hidden">
            {product.thumbnail && (
              <div className="aspect-video relative">
                <img
                  src={product.thumbnail}
                  alt={product.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg line-clamp-2">
                    {product.title}
                  </h3>
                  {product.description && (
                    <p className="text-gray-600 text-sm mt-2 line-clamp-3">
                      {product.description}
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-green-600">
                    {formatCurrency(product.price)}
                  </span>
                  <Badge variant="secondary">
                    {product.affiliateCommission || 10}% comissão
                  </Badge>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>Por {product.creator.name || product.creator.email}</span>
                  {product.category && (
                    <Badge variant="outline">{product.category.name}</Badge>
                  )}
                </div>

                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {product._count.affiliateLinks} afiliados
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {product._count.orders} vendas
                  </div>
                </div>

                <Button
                  onClick={() => handleRequestAffiliation(product.id)}
                  disabled={affiliatingProductId === product.id}
                  className="w-full"
                >
                  {affiliatingProductId === product.id ? (
                    'Solicitando...'
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Tornar-se Afiliado
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Paginação */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-4">
          <Link
            href={`/afiliados/produtos?${new URLSearchParams({
              ...searchParams,
              page: (pagination.page - 1).toString(),
            }).toString()}`}
            className={pagination.hasPrev ? '' : 'pointer-events-none opacity-50'}
          >
            <Button variant="outline" disabled={!pagination.hasPrev}>
              Anterior
            </Button>
          </Link>

          <span className="text-sm text-gray-600">
            Página {pagination.page} de {pagination.totalPages}
          </span>

          <Link
            href={`/afiliados/produtos?${new URLSearchParams({
              ...searchParams,
              page: (pagination.page + 1).toString(),
            }).toString()}`}
            className={pagination.hasNext ? '' : 'pointer-events-none opacity-50'}
          >
            <Button variant="outline" disabled={!pagination.hasNext}>
              Próxima
            </Button>
          </Link>
        </div>
      )}

      {/* Empty State */}
      {products.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum produto encontrado
          </h3>
          <p className="text-gray-600 mb-4">
            Tente ajustar seus filtros ou buscar por outros termos
          </p>
          <Button onClick={() => {
            setSearch('');
            setSelectedCategory('');
            router.push('/afiliados/produtos');
          }}>
            Limpar filtros
          </Button>
        </div>
      )}
    </div>
  );
}
