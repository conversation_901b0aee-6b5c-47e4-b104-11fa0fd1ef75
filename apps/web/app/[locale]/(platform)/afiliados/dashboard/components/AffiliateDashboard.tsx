'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
  TrendingUp,
  <PERSON>,
  MousePointer,
  DollarSign,
  Eye,
  Copy,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface AffiliateDashboardProps {
  affiliateProfile: {
    id: string;
    commission: number | null;
    totalSales: number;
    affiliateLinks: Array<{
      id: string;
      code: string;
      clicks: number;
      conversions: number;
      product: {
        id: string;
        title: string;
        price: number;
        thumbnail: string | null;
        creator: {
          name: string | null;
          email: string;
        };
      };
    }>;
  };
  stats: {
    totalClicks: number;
    totalConversions: number;
    totalCommissions: number;
    conversionRate: number;
  };
  recentSales: Array<{
    id: string;
    amount: number;
    createdAt: Date;
    product: {
      title: string;
      thumbnail: string | null;
    };
    user: {
      name: string | null;
      email: string;
    };
  }>;
}

export function AffiliateDashboard({ affiliateProfile, stats, recentSales }: AffiliateDashboardProps) {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(new Date(date));
  };

  const copyAffiliateLink = async (code: string) => {
    const link = `${window.location.origin}/a/${code}`;
    await navigator.clipboard.writeText(link);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Dashboard do Afiliado
          </h1>
          <p className="text-gray-600 mt-2">
            Acompanhe suas vendas e performance de afiliação
          </p>
        </div>
        <div className="flex gap-3">
          <Link href="/afiliados/produtos">
            <Button variant="outline">
              Buscar Produtos
            </Button>
          </Link>
          <Link href="/afiliados/relatorios">
            <Button>
              Ver Relatórios
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total de Cliques
            </CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClicks}</div>
            <p className="text-xs text-muted-foreground">
              Em todos os seus links
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Conversões
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalConversions}</div>
            <p className="text-xs text-muted-foreground">
              Vendas realizadas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Taxa de Conversão
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.conversionRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Cliques que viraram vendas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Comissões Totais
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(stats.totalCommissions)}
            </div>
            <p className="text-xs text-muted-foreground">
              Valor total ganho
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Meus Produtos de Afiliação */}
        <Card>
          <CardHeader>
            <CardTitle>Meus Produtos de Afiliação</CardTitle>
            <CardDescription>
              Produtos que você está promovendo como afiliado
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {affiliateProfile.affiliateLinks.length > 0 ? (
                affiliateProfile.affiliateLinks.map((link) => (
                  <div key={link.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {link.product.thumbnail && (
                        <img
                          src={link.product.thumbnail}
                          alt={link.product.title}
                          className="w-12 h-12 object-cover rounded"
                        />
                      )}
                      <div>
                        <h3 className="font-medium">{link.product.title}</h3>
                        <p className="text-sm text-gray-600">
                          Por {link.product.creator.name || link.product.creator.email}
                        </p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-xs text-gray-500">
                            {link.clicks} cliques
                          </span>
                          <span className="text-xs text-gray-500">
                            {link.conversions} vendas
                          </span>
                          <span className="text-xs font-medium text-green-600">
                            {formatCurrency(link.product.price)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyAffiliateLink(link.code)}
                      >
                        {copiedCode === link.code ? (
                          'Copiado!'
                        ) : (
                          <>
                            <Copy className="h-3 w-3 mr-1" />
                            Copiar Link
                          </>
                        )}
                      </Button>
                      <Link href={`/afiliados/produtos/${link.product.id}`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Você ainda não é afiliado de nenhum produto
                  </p>
                  <Link href="/afiliados/produtos">
                    <Button>
                      Buscar Produtos para Afiliar
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Vendas Recentes */}
        <Card>
          <CardHeader>
            <CardTitle>Vendas Recentes</CardTitle>
            <CardDescription>
              Suas últimas vendas como afiliado
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentSales.length > 0 ? (
                recentSales.map((sale) => (
                  <div key={sale.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {sale.product.thumbnail && (
                        <img
                          src={sale.product.thumbnail}
                          alt={sale.product.title}
                          className="w-10 h-10 object-cover rounded"
                        />
                      )}
                      <div>
                        <h4 className="font-medium">{sale.product.title}</h4>
                        <p className="text-sm text-gray-600">
                          {sale.user.name || sale.user.email}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(sale.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-green-600">
                        {formatCurrency(sale.amount)}
                      </p>
                      <Badge variant="secondary" className="text-xs">
                        Pago
                      </Badge>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">
                    Nenhuma venda realizada ainda
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
