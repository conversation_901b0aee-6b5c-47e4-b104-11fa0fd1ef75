import { redirect } from 'next/navigation';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { AffiliateDashboard } from './components/AffiliateDashboard';

export default async function AffiliateDashboardPage() {
  const session = await currentUser();

  if (!session?.user) {
    redirect('/auth/signin');
  }

  // Buscar perfil de afiliado
  const affiliateProfile = await db.affiliateProfile.findUnique({
    where: {
      userId: session.user.id,
    },
    include: {
      affiliateLinks: {
        include: {
          product: {
            include: {
              creator: {
                select: {
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      },
    },
  });

  // Se não tem perfil de afiliado, redirecionar para página de busca de produtos
  if (!affiliateProfile) {
    redirect('/afiliados/produtos');
  }

  // Buscar estatísticas do afiliado
  const stats = await db.affiliateLink.aggregate({
    where: {
      affiliateId: affiliateProfile.id,
    },
    _sum: {
      clicks: true,
      conversions: true,
    },
  });

  // Buscar vendas recentes do afiliado
  const recentSales = await db.order.findMany({
    where: {
      affiliateId: session.user.id,
      status: 'PAID',
    },
    include: {
      product: {
        select: {
          title: true,
          thumbnail: true,
        },
      },
      user: {
        select: {
          name: true,
          email: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 10,
  });

  // Calcular comissões totais
  const totalCommissions = await db.orderItem.aggregate({
    where: {
      order: {
        affiliateId: session.user.id,
        status: 'PAID',
      },
    },
    _sum: {
      affiliateCommission: true,
    },
  });

  return (
    <AffiliateDashboard
      affiliateProfile={affiliateProfile}
      stats={{
        totalClicks: stats._sum.clicks || 0,
        totalConversions: stats._sum.conversions || 0,
        totalCommissions: totalCommissions._sum.affiliateCommission || 0,
        conversionRate: stats._sum.clicks ? ((stats._sum.conversions || 0) / stats._sum.clicks * 100) : 0,
      }}
      recentSales={recentSales}
    />
  );
}
