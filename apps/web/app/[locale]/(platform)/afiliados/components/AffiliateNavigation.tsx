'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@ui/lib';
import { Button } from '@ui/components/button';
import {
  LayoutDashboard,
  Package,
  TrendingUp,
  DollarSign,
  Settings,
  Menu,
  X,
  UserCheck,
  ExternalLink,
} from 'lucide-react';

const navigation = [
  {
    name: 'Dashboard',
    href: '/afiliados/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Buscar Produtos',
    href: '/afiliados/produtos',
    icon: Package,
  },
  {
    name: 'Meus Links',
    href: '/afiliados/links',
    icon: ExternalLink,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    href: '/afiliados/relatorios',
    icon: TrendingUp,
  },
  {
    name: 'Gan<PERSON>',
    href: '/afiliados/ganhos',
    icon: DollarSign,
  },
  {
    name: 'Configurações',
    href: '/afiliados/configuracoes',
    icon: Settings,
  },
];

export function AffiliateNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Sidebar */}
      <aside
        className={cn(
          'fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transition-transform lg:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-8 w-8 text-blue-600" />
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Afiliados</h2>
                <p className="text-xs text-gray-500">Buscar Inovar</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  <item.icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <Link
              href="/app/dashboard"
              className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              <LayoutDashboard className="h-5 w-5" />
              <span>Dashboard Principal</span>
            </Link>
          </div>
        </div>
      </aside>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
