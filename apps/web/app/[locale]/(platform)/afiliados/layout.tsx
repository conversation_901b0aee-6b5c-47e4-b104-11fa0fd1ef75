import { redirect } from 'next/navigation';
import { currentUser } from '@saas/auth/lib/current-user';
import { AffiliateNavigation } from './components/AffiliateNavigation';

export default async function AffiliateLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await currentUser();

  if (!session?.user) {
    redirect('/auth/signin');
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <AffiliateNavigation />
      <main className="flex-1 overflow-y-auto">
        {children}
      </main>
    </div>
  );
}
